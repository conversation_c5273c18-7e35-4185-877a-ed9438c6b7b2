import { Pinecone } from "@pinecone-database/pinecone";
import { Type } from "@sinclair/typebox";
import { NonRetriableError } from "inngest";
import * as z from "zod";

import { campaignHandlers } from "@kalos/database/handlers/campaign";
import { campaignGroupSegmentHandlers } from "@kalos/database/handlers/campaignGroupSegment";
import { campaignLinkedInAudienceTargetGroupHandlers } from "@kalos/database/handlers/campaignLinkedInAudienceTargetGroup";
import { campaignLinkedInAudienceTargetGroupTargetHandlers } from "@kalos/database/handlers/campaignLinkedInAudienceTargetGroupTarget";
import { linkedInCampaignAudienceTargetHandlers } from "@kalos/database/handlers/linkedInCampaignAudienceTarget";
import { segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers } from "@kalos/database/handlers/segmentLinkedInAudienceTargetGroupPerfabGroupTarget";
import { segmentLinkedInAudienceTargetGroupPrefabHandlers } from "@kalos/database/handlers/segmentLinkedInAudienceTargetGroupPrefab";
import { segmentLinkedInAudienceTargetGroupPrefabGroupHandlers } from "@kalos/database/handlers/segmentLinkedInAudienceTargetGroupPrefabGroup";
import {
  advertisingLangfuseClient,
  callLangfuseOpenaiText,
  getEmbedding,
  unstructuredToStructuredText,
} from "@kalos/llms/utils";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { inngest } from "../inngest/client";

const pineconeApiKey = process.env.PINECONE_API_KEY;
if (!pineconeApiKey) {
  throw new Error("PINECONE_API_KEY is not set");
}

const pc = new Pinecone({
  apiKey: pineconeApiKey,
});
const index = pc.Index("linkedin-facet-entities");

const NumberOfEmployees = z.object({
  lowBound: z.number().nullable().optional(),
  highBound: z.number().nullable().optional(),
});

const AnnualRevenue = z.object({
  lowBound: z.number().nullable().optional(),
  highBound: z.number().nullable().optional(),
});

const mapKalosDataToLinkedInDataEventBody = z.object({
  verticals: z.array(z.string()).nullable().optional(),
  verticalEntities: z.array(z.string()),
  numberOfEmployees: NumberOfEmployees.nullable().optional(),
  annualRevenue: AnnualRevenue.nullable().optional(),
  jobFunction: z.string().nullable().optional(),
  jobSeniority: z.string().nullable().optional(),
  jobTitles: z.array(z.string()).nullable().optional(),
  campaignId: z.string(),
  titles: z.boolean(),
});

export type MapKalosDataToLinkedInDataEventBody = z.infer<
  typeof mapKalosDataToLinkedInDataEventBody
>;

//def map_to_linkedIn_facet_entity(field: str, facet_urn: str, value: str):

export async function facetEntityMappingAgent(
  field: string,
  facetUrn: string,
  value: string,
) {
  const embedding = await getEmbedding(value);
  if (!embedding) {
    throw new NonRetriableError("No embedding found");
  }
  const vectorQRes = await index.query({
    vector: embedding,
    filter: {
      facet_urn: { $eq: facetUrn },
    },
    topK: 500,
    includeMetadata: true,
  });
  console.log(vectorQRes.matches);
  const matches = vectorQRes.matches;
  let entitiesAsStr = "[\n";
  for (const match of matches) {
    const urn = match.metadata?.entity_urn;
    const name = match.metadata?.name;
    if (!urn || !name) {
      continue;
    }
    const jsonObjForStr = JSON.stringify({ name: name, urn: urn });
    entitiesAsStr += `${jsonObjForStr},\n`;
  }
  entitiesAsStr += "]\n";
  console.log(entitiesAsStr);
  const res = await callLangfuseOpenaiText({
    langfuseClient: advertisingLangfuseClient,
    model: "gpt-4o",
    promptName: "kalos-entity-mapping",
    temperature: 0,
    promptVariables: {
      field: field,
      facet_entities: entitiesAsStr,
      field_value: value,
    },
  });

  if (!res) {
    throw new NonRetriableError("No response from OpenAI");
  }
  console.log(res);
  const facetEntityJsonSchema = Type.Object({
    facetEntities: Type.Array(
      Type.Object({
        name: Type.String(),
        urn: Type.String(),
      }),
    ),
  });
  const structuredRes = await unstructuredToStructuredText({
    text: res,
    jsonSchema: facetEntityJsonSchema,
  });
  if (!structuredRes) {
    throw new NonRetriableError("No structured response from OpenAI");
  }
  return structuredRes;
}
async function mapKalosToLinkedInFacetEntity(
  field: string,
  facetUrn: string,
  value: string,
) {
  const embedding = await getEmbedding(value);
  if (!embedding) {
    throw new NonRetriableError("No embedding found");
  }
  const vectorQRes = await index.query({
    vector: embedding,
    filter: {
      facet_urn: { $eq: facetUrn },
    },
    topK: 25,
    includeMetadata: true,
  });
  console.log(vectorQRes.matches);
  const matches = vectorQRes.matches;
  let entitiesAsStr = "[\n";
  for (const match of matches) {
    const urn = match.metadata?.entity_urn;
    const name = match.metadata?.name;
    if (!urn || !name) {
      continue;
    }
    const jsonObjForStr = JSON.stringify({ name: name, urn: urn });
    entitiesAsStr += `${jsonObjForStr}\n`;
  }
  entitiesAsStr += "]\n";
  console.log(entitiesAsStr);
  const res = await callLangfuseOpenaiText({
    langfuseClient: advertisingLangfuseClient,
    model: "gpt-4o",
    promptName: "map-kalos-field-value-linkedIn-facet-entity",
    temperature: 0,
    promptVariables: {
      field,
      facets: entitiesAsStr,
      value,
    },
  });

  if (!res) {
    throw new NonRetriableError("No response from OpenAI");
  }
  const facetEntityJsonSchema = Type.Object({
    name: Type.String(),
    urn: Type.String(),
  });
  const structuredRes = await unstructuredToStructuredText({
    text: res,
    jsonSchema: facetEntityJsonSchema,
  });
  if (!structuredRes) {
    throw new NonRetriableError("No structured response from OpenAI");
  }
  return structuredRes;
}

async function mapKalosJobTitlesToLinkedInJobTitles(
  titles: string[],
  jobFunction: string,
  jobSeniority: string,
) {
  const embedding = await getEmbedding(
    `${jobFunction} ${jobSeniority} ${titles.join(" ")}`,
  );
  if (!embedding) {
    throw new NonRetriableError("No embedding found");
  }
  const vectorQRes = await index.query({
    vector: embedding,
    filter: {
      facet_urn: { $eq: "urn:li:adTargetingFacet:titles" },
    },
    topK: 50,
    includeMetadata: true,
  });
  const matches = vectorQRes.matches;
  let entitiesAsStr = "[\n";
  for (const match of matches) {
    const urn = match.metadata?.entity_urn;
    const name = match.metadata?.name;
    if (!urn || !name) {
      continue;
    }
    const jsonObjForStr = JSON.stringify({ name: name, urn: urn });
    entitiesAsStr += `${jsonObjForStr}\n`;
  }

  entitiesAsStr += "]\n";

  const res = await callLangfuseOpenaiText({
    langfuseClient: advertisingLangfuseClient,
    model: "gpt-4o",
    promptName:
      "map-job-function-seniority-and-titles-to-linkedIn-job-title-facet-entities",
    temperature: 0,
    promptVariables: {
      crm_job_titles: titles.join("\n"),
      function: jobFunction,
      seniority: jobSeniority,
      facets: entitiesAsStr,
    },
  });

  if (!res) {
    throw new NonRetriableError("No response from OpenAI");
  }

  const jobTitleJsonSchema = Type.Object({
    urn: Type.String(),
    name: Type.String(),
    isEncompassed: Type.Boolean(),
  });
  const jobTitleListJsonSchema = Type.Object({
    jobTitles: Type.Array(jobTitleJsonSchema),
  });

  const structuredRes = await unstructuredToStructuredText({
    text: res,
    jsonSchema: jobTitleListJsonSchema,
  });
  if (!structuredRes) {
    throw new NonRetriableError("No structured response from OpenAI");
  }
  return structuredRes.jobTitles.filter((jobTitle) => jobTitle.isEncompassed);
}

export async function mapGptGeneratedGroupsToLinkedInGroupsViaAPI(
  gptGeneratedGroups: string[],
  organizationId: number,
): Promise<{ name: string; urn: string }[]> {
  const validGroups: { name: string; urn: string }[] = [];

  try {
    const linkedInClient = await getLinkedInApiClientFromOrganizationId(organizationId);

    if (!linkedInClient) {
      return validGroups;
    }

    for (const group of gptGeneratedGroups) {
      try {
        const linkedInGroups = await linkedInClient.getEntitesViaTypeahead(
          "urn:li:adTargetingFacet:groups",
          group
        );

        if (linkedInGroups && linkedInGroups.length > 0) {
          const bestMatch = linkedInGroups[0];
          if (bestMatch && bestMatch.name && bestMatch.urn) {
            validGroups.push({
              name: bestMatch.name,
              urn: bestMatch.urn
            });
          }
        } else {
        }
      } catch (error) {
        console.error(`Error searching LinkedIn for group "${group}":`, error);
      }
    }
  } catch (error) {
    console.error("Error initializing LinkedIn API client for groups:", error);
  }

  return validGroups;
}

export async function mapGptGeneratedSkillsToLinkedInSkillsViaAPI(
  gptGeneratedSkills: string[],
  organizationId: number,
): Promise<{ name: string; urn: string }[]> {
  const validSkills: { name: string; urn: string }[] = [];

  try {
    const linkedInClient = await getLinkedInApiClientFromOrganizationId(organizationId);

    if (!linkedInClient) {
      return validSkills;
    }

    for (const skill of gptGeneratedSkills) {
      try {
        const linkedInSkills = await linkedInClient.getEntitesViaTypeahead(
          "urn:li:adTargetingFacet:skills",
          skill
        );

        if (linkedInSkills && linkedInSkills.length > 0) {
          const bestMatch = linkedInSkills[0];
          if (bestMatch && bestMatch.name && bestMatch.urn) {
            validSkills.push({
              name: bestMatch.name,
              urn: bestMatch.urn
            });
          }
        } else {
        }
      } catch (error) {
        console.error(`Error searching LinkedIn for skill "${skill}":`, error);
      }
    }

  } catch (error) {
    console.error("Error initializing LinkedIn API client:", error);
  }

  return validSkills;
}

export const mapKalosDataToLinkedInData = inngest.createFunction(
  { id: "map-kalos-data-to-linkedin-data" },
  { event: "linkedin/map-kalos-data-to-linkedin-data" },
  async ({ step, event }) => {
    const parsedBody = mapKalosDataToLinkedInDataEventBody.safeParse(
      event.data,
    );
    if (!parsedBody.success) {
      throw new NonRetriableError("Invalid event data");
    }
    const body = parsedBody.data;

    const verticalEntityStep = step.run("get-vertical-entity", async () => {
      if (!body.verticals) {
        return null;
      }

      const res: {
        facetEntities: {
          name: string;
          urn: string;
        }[];
      } = {
        facetEntities: [],
      };

      for (const eachVertical of body.verticals) {
        const currRes = await facetEntityMappingAgent(
          "Vertical",
          "urn:li:adTargetingFacet:industries",
          eachVertical,
        );
        res.facetEntities.push(...currRes.facetEntities);
      }

      return await facetEntityMappingAgent(
        "Vertical",
        "urn:li:adTargetingFacet:industries",
        body.verticals.join(", "),
      );
    });

    const numberOfEmployeesStep = step.run(
      "get-number-of-employees-entity",
      async () => {
        if (!body.numberOfEmployees) {
          return null;
        }
        return await mapKalosToLinkedInFacetEntity(
          "Number of Employees",
          "urn:li:adTargetingFacet:staffCountRanges",
          body.numberOfEmployees.toString(),
        );
      },
    );

    const annualRevenueStep = step.run(
      "get-annual-revenue-entity",
      async () => {
        if (!body.annualRevenue) {
          return null;
        }
        if (!body.annualRevenue.lowBound && !body.annualRevenue.highBound) {
          return null;
        }
        if (body.annualRevenue.lowBound && !body.annualRevenue.highBound) {
          return await facetEntityMappingAgent(
            "Annual Revenue",
            "urn:li:adTargetingFacet:revenue",
            `>${body.annualRevenue.lowBound}`,
          );
        }
        if (!body.annualRevenue.lowBound && body.annualRevenue.highBound) {
          return await facetEntityMappingAgent(
            "Annual Revenue",
            "urn:li:adTargetingFacet:revenue",
            `<${body.annualRevenue.highBound}`,
          );
        }
        return await facetEntityMappingAgent(
          "Annual Revenue",
          "urn:li:adTargetingFacet:revenue",
          `${body.annualRevenue.lowBound} - ${body.annualRevenue.highBound}`,
        );
      },
    );

    const jobFunctionStep = step.run("get-job-function-entity", async () => {
      if (!body.jobFunction || body.titles) {
        return null;
      }
      return await mapKalosToLinkedInFacetEntity(
        "Job Function",
        "urn:li:adTargetingFacet:jobFunctions",
        body.jobFunction,
      );
    });

    /* job_titles_entities: FacetEntityList | None = None
  if data.job_function is not None and data.job_titles is not None and data.job_seniority is not None and data.titles is True:
      job_titles_entities = map_function_to_title(data.job_titles, data.job_function, data.job_seniority)*/

    const jobTitlesStep = step.run("get-job-titles-entities", async () => {
      if (
        !body.jobFunction ||
        !body.jobTitles ||
        !body.jobSeniority ||
        !body.titles
      ) {
        return null;
      }
      return await mapKalosJobTitlesToLinkedInJobTitles(
        body.jobTitles,
        body.jobFunction,
        body.jobSeniority,
      );
    });

    const [
      verticalEntity,
      numberOfEmployeesEntity,
      annualRevenueEntity,
      jobFunctionEntity,
      jobTitlesEntity,
    ] = await Promise.all([
      verticalEntityStep,
      numberOfEmployeesStep,
      annualRevenueStep,
      jobFunctionStep,
      jobTitlesStep,
    ]);

    await step.run("save", async () => {
      const data = {
        vertical: verticalEntity,
        number_of_employees: numberOfEmployeesEntity,
        annual_revenue: annualRevenueEntity,
        campaign_id: body.campaignId,
        job_titles: jobTitlesEntity,
        job_function: jobFunctionEntity,
      };
      const cgs = await campaignGroupSegmentHandlers.select.one.byCampaignId(
        data.campaign_id,
      );
      if (!cgs) {
        throw new Error("Campaign group segment not found");
      }

      const campaign = await campaignHandlers.select.one.byId(data.campaign_id);
      if (!campaign) {
        throw new Error("Campaign not found");
      }
      const prefabs =
        await segmentLinkedInAudienceTargetGroupPrefabHandlers.select.byManySegmentIdsWithGroupAndTarget(
          [cgs.campaign_group_segment.segmentId],
        );

      const prefab = prefabs.find(
        (each) => each.description === campaign.description,
      );
      if (prefab) {
        const targetGroupIds: string[] = [];
        const targetIds: string[] = [];
        for (const eachTargetGroup of prefab.targetGroups) {
          targetGroupIds.push(eachTargetGroup.targetGroupId);
          targetIds.push(
            ...eachTargetGroup.targets.map((each) => each.targetId),
          );
        }
        await Promise.all([
          segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.delete.many.byId(
            targetIds,
          ),
          segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.delete.many.byId(
            targetGroupIds,
          ),
          segmentLinkedInAudienceTargetGroupPrefabHandlers.delete.many.byId([
            prefab.prefabId,
          ]),
        ]);
      }

      const newPrefab =
        await segmentLinkedInAudienceTargetGroupPrefabHandlers.insert.many([
          {
            segmentId: cgs.campaign_group_segment.segmentId,
            description: campaign.description,
          },
        ]);
      const newPrefabId = newPrefab[0]?.id;
      if (!newPrefabId) {
        throw new Error("Failed to insert new prefab");
      }

      if (data.vertical) {
        const group =
          await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
            campaignId: data.campaign_id,
            facetUrn: "urn:li:adTargetingFacet:industries",
            facetName: "Industries",
          });

        const prefabGroup =
          await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
            [
              {
                segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
                facetUrn: "urn:li:adTargetingFacet:industries",
                facetName: "Industries",
              },
            ],
          );
        for (const eachFacetEntity of data.vertical.facetEntities) {
          let target = await linkedInCampaignAudienceTargetHandlers.select.one(
            eachFacetEntity.urn,
          );
          if (!target) {
            target = await linkedInCampaignAudienceTargetHandlers.insert.one({
              linkedInFacetEntityUrn: eachFacetEntity.urn,
              linkedInFacetUrn: "urn:li:adTargetingFacet:industries",
              name: eachFacetEntity.name,
            });
          }
          await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
            {
              campaignLinkedInAudienceTargetGroupId: group.id,
              linkedInAudienceTargetId: target.id,
            },
          ]);
          if (prefabGroup[0] !== undefined) {
            await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
              [
                {
                  segmentLinkedInAudienceTargetGroupPrefabGroupId:
                    prefabGroup[0].id,
                  linkedinTargetAudienceId: target.id,
                },
              ],
            );
          }
          console.log("Inserted vertical");
        }
      }
      if (data.annual_revenue) {
        const group =
          await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
            campaignId: data.campaign_id,
            facetUrn: "urn:li:adTargetingFacet:revenue",
            facetName: "Revenue",
          });

        const prefabGroup =
          await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
            [
              {
                segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
                facetUrn: "urn:li:adTargetingFacet:revenue",
                facetName: "Revenue",
              },
            ],
          );

        for (const eachFacetEntity of data.annual_revenue.facetEntities) {
          let target = await linkedInCampaignAudienceTargetHandlers.select.one(
            eachFacetEntity.urn,
          );
          if (!target) {
            target = await linkedInCampaignAudienceTargetHandlers.insert.one({
              linkedInFacetEntityUrn: eachFacetEntity.urn,
              linkedInFacetUrn: "urn:li:adTargetingFacet:revenue",
              name: eachFacetEntity.name,
            });
          }
          await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
            {
              campaignLinkedInAudienceTargetGroupId: group.id,
              linkedInAudienceTargetId: target.id,
            },
          ]);
          if (prefabGroup[0] !== undefined) {
            await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
              [
                {
                  segmentLinkedInAudienceTargetGroupPrefabGroupId:
                    prefabGroup[0].id,
                  linkedinTargetAudienceId: target.id,
                },
              ],
            );
          }
        }

        console.log("Inserted annual revenue");
      }
      if (data.job_function) {
        const group =
          await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
            campaignId: data.campaign_id,
            facetUrn: "urn:li:adTargetingFacet:jobFunctions",
            facetName: "Job Functions",
          });

        const prefabGroup =
          await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
            [
              {
                segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
                facetUrn: "urn:li:adTargetingFacet:jobFunctions",
                facetName: "Functions",
              },
            ],
          );

        let target = await linkedInCampaignAudienceTargetHandlers.select.one(
          data.job_function.urn,
        );
        if (!target) {
          target = await linkedInCampaignAudienceTargetHandlers.insert.one({
            linkedInFacetEntityUrn: data.job_function.urn,
            linkedInFacetUrn: "urn:li:adTargetingFacet:jobFunctions",
            name: data.job_function.name,
          });
        }
        await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
          {
            campaignLinkedInAudienceTargetGroupId: group.id,
            linkedInAudienceTargetId: target.id,
          },
        ]);
        if (prefabGroup[0] !== undefined) {
          await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
            [
              {
                segmentLinkedInAudienceTargetGroupPrefabGroupId:
                  prefabGroup[0].id,
                linkedinTargetAudienceId: target.id,
              },
            ],
          );
        }
      }

      if (data.job_titles) {
        const group =
          await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
            campaignId: data.campaign_id,
            facetUrn: "urn:li:adTargetingFacet:titles",
            facetName: "Titles",
          });

        const prefabGroup =
          await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
            [
              {
                segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
                facetUrn: "urn:li:adTargetingFacet:titles",
                facetName: "Titles",
              },
            ],
          );
        for (const jt of data.job_titles) {
          let target = await linkedInCampaignAudienceTargetHandlers.select.one(
            jt.urn,
          );
          if (!target) {
            target = await linkedInCampaignAudienceTargetHandlers.insert.one({
              linkedInFacetEntityUrn: jt.urn,
              linkedInFacetUrn: "urn:li:adTargetingFacet:titles",
              name: jt.name,
            });
          }
          await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
            {
              campaignLinkedInAudienceTargetGroupId: group.id,
              linkedInAudienceTargetId: target.id,
            },
          ]);

          if (prefabGroup[0] !== undefined) {
            await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
              [
                {
                  segmentLinkedInAudienceTargetGroupPrefabGroupId:
                    prefabGroup[0].id,
                  linkedinTargetAudienceId: target.id,
                },
              ],
            );
          }
        }
      }
      await campaignHandlers.update.audiencePopulated(data.campaign_id, true);
    });
  },
);
