import { foreign<PERSON>ey, varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdSegmentValuePropTable } from "./linkedInAdSegmentValueProp.table";
import { linkedInCampaignTable } from "./linkedInCampaign.table";
import { linkedInDeploymentConfigTable } from "./linkedInDeploymentConfig.table";
import { socialPostCallToActionCopyTable } from "./socialPostCallToActionCopy.table";
import { socialPostCopyTable } from "./socialPostCopy.table";

export const sponsoredContentDeploymentConfigVarientMapTable = adSchema.table(
  "sponsored_content_deployment_config_varient_map",
  {
    id: uuIdCol,
    linkedInDeploymentConfigId: uuIdFk("linkedin_deployment_config_id")
      .notNull()
      .references(() => linkedInDeploymentConfigTable.id),
    audienceId: uuIdFk("audience_id")
      .notNull()
      .references(() => linkedInCampaignTable.linkedInAudienceId),
    valuePropId: uuIdFk("value_prop_id")
      .notNull()
      .references(() => linkedInAdSegmentValuePropTable.id),
    socialPostBodyCopyType: varchar("social_post_body_copy_type", {
      length: 255,
    }).notNull(),
    socialPostCtaCopyType: varchar("social_post_cta_copy_type", {
      length: 255,
    }).notNull(),
  },
  (t) => ({
    socialPostBodyCopyTypeFk: foreignKey({
      columns: [t.valuePropId, t.socialPostBodyCopyType],
      foreignColumns: [
        socialPostCopyTable.linkedInAdSegmentValuePropId,
        socialPostCopyTable.socialPostCopyType,
      ],
      name: "deploymentConfigSponsoredContentValuePropAndCopyTypeFk",
    }),
    socialPostCtaCopyTypeFk: foreignKey({
      columns: [t.valuePropId, t.socialPostCtaCopyType],
      foreignColumns: [
        socialPostCallToActionCopyTable.adSegmentValuePropId,
        socialPostCallToActionCopyTable.type,
      ],
      name: "deploymentConfigSponsoredContentValuePropAndCtaTypeFk",
    }),
  }),
);
