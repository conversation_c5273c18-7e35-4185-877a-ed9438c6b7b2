import type {
  AssociatedId,
  BatchReadInputSimplePublicObjectId,
  PublicObjectSearchRequest,
} from "@hubspot/api-client/lib/codegen/crm/companies";
import type {
  // Use types suggested by linter
  <PERSON>chResponseSimplePublicObject,
  BatchResponseSimplePublicObjectWithErrors,
  BatchResponseSimplePublicObjectWithErrors as BatchResponseSimplePublicObjectWithErrorsType, // Alias
  // Type for errors in batch responses
  ErrorDetail,
  SimplePublicObject,
} from "@hubspot/api-client/lib/codegen/crm/objects";
import { Client } from "@hubspot/api-client";
import { PublicAssociationMulti } from "@hubspot/api-client/lib/codegen/crm/associations";
import {
  Filter,
  FilterOperatorEnum,
} from "@hubspot/api-client/lib/codegen/crm/companies";
import { PostHog } from "posthog-node";

import { ExternalCrmProvider } from "../../application/interfaces/infrastructure/externalCrm.provider.interface";
import { HubspotCredentials } from "../../domain/entities/hubspotCredentials";
import { LifecycleStage } from "../../domain/types/types";

// Define a helper type for the structured meeting data we want
interface HubSpotMeeting {
  id: string; // HubSpot meeting ID
  subject?: string;
  startTime?: Date;
  endTime?: Date;
  // Add any other relevant fields, e.g., outcome, raw metadata
  metadata?: Record<string, any>;
  // We might need the raw properties object later for metadata
  rawProperties?: { [key: string]: string | null };
  status?: MeetingStatus;
}

type MeetingStatus =
  | "scheduled"
  | "completed"
  | "canceled"
  | "no_show"
  | "rescheduled";

export class HubspotProvider implements ExternalCrmProvider {
  posthogClient = new PostHog(process.env.NEXT_PUBLIC_POSTHOG_KEY ?? "", {
    host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
  });

  private organizationId: number = 0;
  private constructor(
    private readonly hubspotClient: Client,
    organizationId: number = 0,
  ) {
    this.organizationId = organizationId;
  }

  //example hubspot url - meetings.hubspot.com/david4214/30minutes-docketai?uuid=************************************&li_fat_id=7cdf92c5-d314-4a03-8fa8-d40ca5fd637a&utm_campaign=Website+visit&utm_medium=paidsocial&utm_content=*********&utm_source=LinkedIn&account_id=*********

  extractUtmFromUrl(url: string | undefined): Record<string, string> {
    const utm: Record<string, string> = {};
    if (!url) return utm;

    try {
      const parsedUrl = new URL(url);
      const params = parsedUrl.searchParams;

      const source = params.get("utm_source");
      const campaign = params.get("utm_campaign");
      const medium = params.get("utm_medium");
      const term = params.get("utm_term");
      const content = params.get("utm_content");
      const channel = params.get("utm_channel");

      if (source) utm.utm_source = source;
      if (campaign) utm.utm_campaign = campaign;
      if (medium) utm.utm_medium = medium;
      if (term) utm.utm_term = term;
      if (content) utm.utm_content = content;
      if (channel) utm.utm_channel = channel;
    } catch (e) {
      console.warn("Failed to parse UTM from URL:", url);
    }

    return utm;
  }

  async getSchema(objectType: "account" | "opportunity" | "contact"): Promise<
    {
      field: string;
      type: "string" | "number" | "date" | "datetime" | "stringArray" | "bool";
    }[]
  > {
    let c = undefined;

    switch (objectType) {
      case "account":
        c = await this.hubspotClient.crm.properties.coreApi.getAll("companies");
        break;
      case "opportunity":
        c = await this.hubspotClient.crm.properties.coreApi.getAll("deals");
        break;
      case "contact":
        c = await this.hubspotClient.crm.properties.coreApi.getAll("contacts");
        break;
    }
    const abc =
      await this.hubspotClient.crm.objects.leads.basicApi.getById("abc");

    if (!c) {
      throw new Error("No properties found for object type");
    }

    const res: {
      field: string;
      type: "string" | "number" | "date" | "datetime" | "stringArray" | "bool";
    }[] = [];
    for (const property of c.results) {
      const propertyType = property.type;
      switch (propertyType) {
        case "string":
          res.push({
            field: property.name,
            type: "string",
          });
          break;
        case "number":
          res.push({
            field: property.name,
            type: "number",
          });
          break;
        case "date":
          res.push({
            field: property.name,
            type: "date",
          });
          break;
        case "datetime":
          res.push({
            field: property.name,
            type: "datetime",
          });
          break;
        case "enumeration":
          res.push({
            field: property.name,
            type: "stringArray",
          });
          break;
        case "bool":
          res.push({
            field: property.name,
            type: "bool",
          });
          break;
      }
    }
    return res;
  }

  // Get Meetings
  // Get All Meetings with associations
  // For each meeting, check if associated contact exists in kalos db
  // if it does, create engagement

  // After initial pull
  // Get recent meetings (last 4 hours)
  /**
   * Fetches meetings from HubSpot with pagination, either doing a full data pull or just recent meetings.
   * Yields batches of meetings with their associated contact IDs to make it easy to check which contacts exist in our database.
   *
   * @param doFullDataPull Whether to do a full data pull or just get recent meetings
   * @returns AsyncGenerator that yields batches of meeting objects with their details and associated contact IDs
   */
  async *getMeetings(doFullDataPull: boolean): AsyncGenerator<
    {
      id: string;
      title?: string;
      startTime?: string;
      endTime?: string;
      outcome?: string;
      body?: string;
      status?: MeetingStatus | undefined;
      contactIds: string[];
      rawProperties?: Record<string, any>;
    }[]
  > {
    // Properties we want to retrieve for each meeting
    const properties = [
      "hs_meeting_title",
      "hs_meeting_body",
      "hs_meeting_start_time",
      "hs_meeting_end_time",
      "hs_meeting_outcome",
    ];

    const batchSize = 100; // How many meetings to yield at once

    if (doFullDataPull) {
      // Full data pull with pagination
      let hasMore = true;
      let after: string | undefined = undefined;

      while (hasMore) {
        const response =
          await this.hubspotClient.crm.objects.meetings.basicApi.getPage(
            batchSize, // Page size
            after, // Pagination token
            properties,
            undefined,
            ["contacts"], // Include associations to contacts
            undefined,
            undefined,
          );

        // Process the results from this page
        if (response.results && response.results.length > 0) {
          const batchMeetings = response.results.map((meeting) => {
            const meetingStatus =
              meeting.properties?.hs_meeting_outcome?.toLocaleLowerCase();
            const status =
              meetingStatus === "no show"
                ? "no_show"
                : ((meetingStatus as MeetingStatus) ?? undefined);
            return {
              id: meeting.id,
              title: meeting.properties?.hs_meeting_title ?? undefined,
              body: meeting.properties?.hs_meeting_body ?? undefined,
              startTime: meeting.properties?.hs_meeting_start_time ?? undefined,
              endTime: meeting.properties?.hs_meeting_end_time ?? undefined,
              outcome: meeting.properties?.hs_meeting_outcome ?? undefined,
              status: status,
              // Extract contact IDs from associations
              contactIds:
                meeting.associations?.contacts?.results?.map(
                  (contact) => contact.id,
                ) || [],
              rawProperties: meeting.properties,
            };
          });

          // Yield this batch of meetings
          yield batchMeetings;
        }

        // Check if there are more pages
        hasMore = !!response.paging?.next?.after;
        after = response.paging?.next?.after;
      }
    } else {
      // Recent meetings (last 4 hours) with pagination
      let hasMore = true;
      let after: string | undefined = undefined;

      while (hasMore) {
        const searchRequest = {
          limit: batchSize,
          after,
          properties,
          sorts: [
            {
              propertyName: "hs_createdate",
              direction: "DESCENDING",
            } as any,
          ],
          filterGroups: [
            {
              filters: [
                {
                  propertyName: "hs_lastmodifieddate",
                  value: new Date(
                    Date.now() - 4 * 60 * 60 * 1000,
                  ).toISOString(),
                  operator: FilterOperatorEnum.Gte,
                },
              ],
            },
          ],
        };

        const response =
          await this.hubspotClient.crm.objects.meetings.searchApi.doSearch(
            searchRequest,
          );

        // Process the results from this page
        if (response.results && response.results.length > 0) {
          // For search results, we need to get associations separately since search doesn't return them
          const meetingIds = response.results.map((meeting) => meeting.id);
          const associationsResponse =
            await this.hubspotClient.crm.associations.batchApi.read(
              "meetings",
              "contacts",
              { inputs: meetingIds.map((id) => ({ id })) },
            );

          // Create a map of meeting ID to contact IDs
          const meetingToContactsMap = new Map<string, string[]>();
          for (const result of associationsResponse.results || []) {
            const contactIds = result.to.map((to) => to.id);
            meetingToContactsMap.set(result._from.id, contactIds);
          }

          // Create a batch of meetings with their contact associations
          const batchMeetings = response.results.map((meeting) => {
            const meetingStatus =
              meeting.properties?.hs_meeting_outcome?.toLocaleLowerCase();
            const status =
              meetingStatus === "no show"
                ? "no_show"
                : ((meetingStatus as MeetingStatus) ?? undefined);
            return {
              id: meeting.id,
              title: meeting.properties?.hs_meeting_title ?? undefined,
              body: meeting.properties?.hs_meeting_body ?? undefined,
              startTime: meeting.properties?.hs_meeting_start_time ?? undefined,
              endTime: meeting.properties?.hs_meeting_end_time ?? undefined,
              outcome: meeting.properties?.hs_meeting_outcome ?? undefined,
              status: status,
              contactIds: meetingToContactsMap.get(meeting.id) || [],
              rawProperties: meeting.properties,
            };
          });

          // Yield this batch of meetings
          yield batchMeetings;
        }

        // Check if there are more pages
        hasMore = !!response.paging?.next?.after;
        after = response.paging?.next?.after;
      }
    }
  }

  /**
   * NOT IN USE, use getMeetings() instead
   * This method is not in use because hubspot does 1 way associations for some reason
   * meetings have associations to contacts but not the other way around
   * This method was built to accomodate that
   * Fetches meetings associated with a list of HubSpot contact IDs.
   *
   * @param contactIds - An array of HubSpot contact IDs.
   * @returns A Promise resolving to a Map where keys are contact IDs and values are arrays of associated HubSpotMeeting objects.
   */
  async getMeetingsForContactIds(
    contactIds: string[],
  ): Promise<Map<string, HubSpotMeeting[]>> {
    const contactToMeetingIdsMap = new Map<string, string[]>();
    const uniqueMeetingIds = new Set<string>();
    const finalResultsMap = new Map<string, HubSpotMeeting[]>(); // Initialize the map to be returned

    if (!contactIds || contactIds.length === 0) {
      return new Map<string, HubSpotMeeting[]>();
    }

    console.log(
      `Fetching meeting associations for ${contactIds.length} contacts using Batch API...`,
    );

    try {
      // Prepare the input for the batch read operation
      const inputs = contactIds.map((id) => ({ id }));

      // Use the batch API to read associations from contacts to meetings
      // The third argument should be of type BatchInputPublicFetchAssociationsBatchRequest
      const batchAssociationRequest = { inputs }; // inputs is contactIds.map(id => ({ id }))

      const batchResponse =
        await this.hubspotClient.crm.associations.batchApi.read(
          "contacts",
          "meetings",
          batchAssociationRequest,
        );

      // Process the results
      // The response structure might contain a 'results' array, where each element corresponds to an input contact
      // Each result element should have the associations for that contact.
      console.log(
        "HubSpot Batch Association response structure:",
        JSON.stringify(batchResponse, null, 2),
      );
      if (batchResponse?.results) {
        batchResponse.results.forEach(
          (result: PublicAssociationMulti, index: number) => {
            const contactId = contactIds[index]; // Get the original contact ID based on index
            if (contactId && result.to?.length > 0) {
              const associatedMeetingIds = result.to
                .map((assoc: AssociatedId) => assoc.id)
                .filter((id: string): id is string => !!id); // Filter out invalid IDs

              if (associatedMeetingIds.length > 0) {
                contactToMeetingIdsMap.set(contactId, associatedMeetingIds);
                associatedMeetingIds.forEach((id: string) =>
                  uniqueMeetingIds.add(id),
                );
              }
            }
          },
        );
      }
    } catch (error: any) {
      console.error(
        `Error fetching batch meeting associations from HubSpot: ${error.message}`,
        error.response?.body || error,
      );
      // Depending on requirements, you might want to re-throw or handle differently
    }

    // Fetch details for unique meetings
    if (uniqueMeetingIds.size === 0) {
      console.log("No associated meetings found for the given contacts.");
      return new Map<string, HubSpotMeeting[]>();
    }

    // 3. Fetch meeting details in batches
    console.log(
      `Fetching details for ${uniqueMeetingIds.size} unique meetings...`,
    );
    const meetingDetailsMap = new Map<string, HubSpotMeeting>();
    const allMeetingIds = Array.from(uniqueMeetingIds);
    const batchSize = 100; // HubSpot batch read limit

    for (let i = 0; i < allMeetingIds.length; i += batchSize) {
      const batchIds = allMeetingIds.slice(i, i + batchSize);
      try {
        // Declare variable with the BatchResponse... union type
        const batchResponse:
          | BatchResponseSimplePublicObject
          | BatchResponseSimplePublicObjectWithErrorsType =
          await this.hubspotClient.crm.objects.meetings.batchApi.read({
            inputs: batchIds.map((id) => ({ id })),
            properties: [
              "hs_meeting_title", // Subject
              "hs_meeting_start_time",
              "hs_meeting_end_time",
              "hs_meeting_outcome",
              // Add other properties if needed
            ],
            propertiesWithHistory: [], // Added missing required property
          });

        // Process successful results
        batchResponse.results.forEach((meetingObj: SimplePublicObject) => {
          const props = meetingObj.properties;

          if (props.hs_meeting_outcome === "no show") {
            props.hs_meeting_outcome = "no_show";
          }

          const meetingStatus =
            (props.hs_meeting_outcome as MeetingStatus) ?? undefined;
          const meeting: HubSpotMeeting = {
            id: meetingObj.id,
            subject: props.hs_meeting_title ?? undefined, // Handle null
            startTime: props.hs_meeting_start_time
              ? new Date(props.hs_meeting_start_time)
              : undefined,
            status: (props.hs_meeting_outcome as MeetingStatus) ?? undefined,
            endTime: props.hs_meeting_end_time
              ? new Date(props.hs_meeting_end_time)
              : undefined,
            metadata: {
              hs_meeting_outcome: props.hs_meeting_outcome ?? undefined,
            }, // Handle null
            rawProperties: props,
          };
          meetingDetailsMap.set(meetingObj.id, meeting);
        });

        // Log errors for meetings that couldn't be fetched
        // Use type guard to safely access errors
        if ("errors" in batchResponse && batchResponse.errors) {
          // Add explicit type ErrorDetail
          batchResponse.errors.forEach((error: ErrorDetail) => {
            console.error(
              `Error fetching meeting details (likely for one of ids ${batchIds.join(",")}): ${error.message}`,
            );
          });
        }
      } catch (error: any) {
        console.error(
          `Failed to fetch batch of meeting details (IDs: ${batchIds.join(", ")}):`,
          error.message,
        );
        // Continue to the next batch
      }
    }

    // 4. Combine data into the final result map
    console.log("Combining association and meeting details...");
    for (const [contactId, associatedMeetingIds] of contactToMeetingIdsMap) {
      const meetingsForContact: HubSpotMeeting[] = [];
      for (const meetingId of associatedMeetingIds) {
        const meetingDetail = meetingDetailsMap.get(meetingId);
        if (meetingDetail) {
          meetingsForContact.push(meetingDetail);
        }
      }
      if (meetingsForContact.length > 0) {
        finalResultsMap.set(contactId, meetingsForContact); // Use finalResultsMap
      }
    }

    console.log("Finished fetching HubSpot meetings.");
    return finalResultsMap; // Return finalResultsMap
  }
  // --- END: New method for fetching meetings ---

  private async *searchApi(
    startDate: Date,
    endDate: Date,
    properties: string[],
    objectType: "contacts" | "companies" | "deals",
    limit?: number,
  ) {
    const poublicObjectSearchRequest: PublicObjectSearchRequest = {
      limit: limit ?? 200,
      properties: properties,
      filterGroups: [
        {
          filters: [
            {
              propertyName: "createdate",
              value: this.formatDate(startDate),
              operator: FilterOperatorEnum.Gte,
            },
            {
              propertyName: "createdate",
              value: this.formatDate(endDate),
              operator: FilterOperatorEnum.Lt,
            },
          ],
        },
      ],
      sorts: [
        {
          propertyName: "hs_object_id",
          direction: "ASCENDING",
        } as any,
      ],
    };

    let res = await this.getSearchApi(objectType, poublicObjectSearchRequest);
    yield res.results;

    let numberOfResult = res.results.length;

    while (res.paging?.next) {
      if (parseInt(res.paging?.next.after) > 9999) {
        const lastId = res.results.map((result) => result.id)[
          res.results.length - 1
        ];
        if (!lastId) {
          throw new Error("No last id found");
        }
        poublicObjectSearchRequest.after = undefined;
        const currentIdFilter =
          poublicObjectSearchRequest.filterGroups?.[0]?.filters.find(
            (filter) => filter.propertyName === "hs_object_id",
          );
        if (!currentIdFilter) {
          poublicObjectSearchRequest.filterGroups?.[0]?.filters.push({
            propertyName: "hs_object_id",
            value: lastId,
            operator: FilterOperatorEnum.Gt,
          });
        } else {
          currentIdFilter.value = lastId;
        }

        res = await this.getSearchApi(objectType, poublicObjectSearchRequest);
      } else {
        poublicObjectSearchRequest.after = res.paging?.next.after;
        res = await this.getSearchApi(objectType, poublicObjectSearchRequest);
      }
      yield res.results;
      numberOfResult += res.results.length;
    }
  }

  private async *leadsSearchApi(
    doFullDataPull: boolean = true,
    properties: string[],
    limit: number = 200,
    startDate: Date = new Date("2024-08-01T00:00:00.000Z"),
  ) {
    const objectType = "contacts";

    const lifecycleStageFilter: Filter = {
      propertyName: "lifecyclestage",
      values: [
        "subscriber",
        "opportunity",
        "lead",
        "marketingqualifiedlead",
        "salesqualifiedlead",
        "evangelist",
        "other",
        "customer",
      ],
      operator: FilterOperatorEnum.In,
    };

    const recentModifiedDate = new Date(Date.now() - 4 * 60 * 60 * 1000); // 4 hours ago

    let searchRequest: PublicObjectSearchRequest = {
      limit,
      properties,
      sorts: [
        {
          propertyName: doFullDataPull ? "hs_object_id" : "lastmodifieddate",
          direction: "ASCENDING",
        } as any,
      ],
      filterGroups: [
        {
          filters: [
            lifecycleStageFilter,
            doFullDataPull
              ? {
                  propertyName: "createdate",
                  operator: FilterOperatorEnum.Gte,
                  value: startDate.getTime().toString(),
                }
              : {
                  propertyName: "lastmodifieddate",
                  operator: FilterOperatorEnum.Gte,
                  value: recentModifiedDate.getTime().toString(),
                },
          ],
        },
      ],
    };

    let lastSeenId: string | undefined = undefined;
    let results: any[] = [];

    while (true) {
      if (doFullDataPull && lastSeenId) {
        // Append hs_object_id > lastSeenId filter for pagination

        const filters = searchRequest?.filterGroups?.[0]?.filters!;
        const existingIdFilterIndex = filters.findIndex(
          (f) => f.propertyName === "hs_object_id",
        );

        if (existingIdFilterIndex >= 0 && filters[existingIdFilterIndex]) {
          filters[existingIdFilterIndex].value = lastSeenId;
        } else {
          filters.push({
            propertyName: "hs_object_id",
            operator: FilterOperatorEnum.Gt,
            value: lastSeenId,
          });
        }
      }

      const res = await this.getSearchApi(objectType, searchRequest);
      results = res.results;

      if (!results || results.length === 0) break;

      yield results;

      if (doFullDataPull) {
        lastSeenId = results[results.length - 1].id;
      } else {
        if (!res.paging?.next?.after) break;
        searchRequest.after = res.paging.next.after;
      }
    }
  }

  private getSearchApi(
    objectType: "contacts" | "companies" | "deals",
    publicObjectSearchRequest: PublicObjectSearchRequest,
  ) {
    return this.hubspotClient.crm[objectType].searchApi.doSearch(
      publicObjectSearchRequest,
    );
  }

  async *getAccounts(
    startDate: Date,
    endDate: Date,
    limit?: number,
  ): AsyncGenerator<
    {
      id: string;
      name: string;
      vertical?: string | null;
      subVertical?: string | null;
      numberOfEmployees?: number | null;
      annualRevenue?: number | null;
      description?: string | null;
      website?: string | null;
      ownerId?: string | null;
    }[]
  > {
    const accountsGenerator = this.searchApi(
      startDate,
      endDate,
      ["name", "owner", "annualrevenue", "numberofemployees", "industry"],
      "companies",
      limit,
    );
    for await (const accounts of accountsGenerator) {
      const res: {
        id: string;
        name: string;
        vertical?: string | null;
        subVertical?: string | null;
        numberOfEmployees?: number | null;
        annualRevenue?: number | null;
        description?: string | null;
        website?: string | null;
        ownerId?: string | null;
      }[] = [];
      for (const account of accounts) {
        const name = account.properties["name"];
        if (!name) {
          continue;
        }
        const vertical = account.properties["industry"];
        const subVertical = undefined;
        const numberOfEmployees = !account.properties["numberofemployees"]
          ? undefined
          : Number(account.properties["numberofemployees"]);
        const annualRevenue = !account.properties["annualrevenue"]
          ? undefined
          : Number(account.properties["annualrevenue"]);
        const description = account.properties["description"];
        const website = account.properties["website"];

        res.push({
          id: account.id,
          name,
          vertical,
          subVertical,
          numberOfEmployees,
          annualRevenue,
          description,
          website,
        });
      }
      yield res;
    }
  }

  async *getOpportunities(
    startDate: Date,
    endDate: Date,
    limit?: number,
  ): AsyncGenerator<
    {
      id: string;
      name: string;
      annualContractValue?: number | null;
      accountId?: string | null;
      isClosed: boolean;
      isWon?: boolean | null;
      closedDate?: Date | null;
      contactId?: string | null;
      createdDate: Date;
    }[]
  > {
    const opportunitesGenerator = this.searchApi(
      startDate,
      endDate,
      ["dealname", "closedate", "hs_is_closed", "hs_is_closed_won", "amount"],
      "deals",
      limit,
    );
    for await (const opportunites of opportunitesGenerator) {
      const res: {
        id: string;
        name: string;
        annualContractValue?: number | null;
        accountId?: string | null;
        isClosed: boolean;
        isWon?: boolean | null;
        closedDate?: Date | null;
        contactId?: string | null;
        createdDate: Date;
      }[] = [];

      const companyAssociationResponse =
        await this.hubspotClient.crm.associations.batchApi.read(
          "deals",
          "companies",
          {
            inputs: opportunites.map((result) => ({ id: result.id })),
          },
        );

      const contactAssociationResponse =
        await this.hubspotClient.crm.associations.batchApi.read(
          "deals",
          "contacts",
          { inputs: opportunites.map((result) => ({ id: result.id })) },
        );

      for (const opportunity of opportunites) {
        const name = opportunity.properties["dealname"];
        const closedDate = opportunity.properties["closedate"];
        const isClosed = opportunity.properties["hs_is_closed"];
        const isWon = opportunity.properties["hs_is_closed_won"];
        const amount = opportunity.properties["amount"];
        const createdDate = opportunity.properties["createdate"];
        if (!createdDate) {
          throw new Error("Opportunity created date is not found");
        }
        if (isClosed == undefined || isClosed == null) {
        }
        if (isWon == undefined || isWon == null) {
        }

        console.log(opportunity);

        res.push({
          id: opportunity.id,
          name: name ?? "",
          annualContractValue: amount ? Number(amount) : undefined,
          closedDate: closedDate ? new Date(closedDate) : undefined,
          isClosed: isClosed === "true",
          isWon: isWon === "true",
          createdDate: new Date(createdDate),
        });
      }

      const deals = res.map((result) => ({
        ...result,
        accountId: companyAssociationResponse.results
          .filter((association) => association._from.id === result.id)
          .flatMap((association) => association.to)
          .map((company) => company.id)[0],
        contactId: contactAssociationResponse.results
          .filter((association) => association._from.id === result.id)
          .flatMap((association) => association.to)
          .map((contact) => contact.id)[0],
      }));

      yield deals;
    }
  }

  async *getContacts(
    startDate: Date,
    endDate: Date,
    limit?: number,
  ): AsyncGenerator<
    {
      id: string;
      accountId?: string | null;
      title?: string | null;
      firstName?: string | null;
      lastName?: string | null;
      email?: string | null;
      phoneNumber?: string | null;
    }[]
  > {
    const contactsGenerator = this.searchApi(
      startDate,
      endDate,
      ["firstname", "lastname", "email", "phone", "jobtitle"],
      "contacts",
      limit,
    );
    for await (const contacts of contactsGenerator) {
      const res: {
        id: string;
        accountId?: string | null;
        title?: string | null;
        firstName?: string | null;
        lastName?: string | null;
        email?: string | null;
        phoneNumber?: string | null;
      }[] = [];

      console.log("GOT CONTACTS");

      const associationsResponse =
        await this.hubspotClient.crm.associations.batchApi.read(
          "contacts",
          "companies",
          {
            inputs: contacts.map((result) => ({ id: result.id })),
          },
        );
      console.log("GOT ASSOCIATIONS");

      for (const contact of contacts) {
        const firstName = contact.properties["firstname"];
        const lastName = contact.properties["lastname"];
        const email = contact.properties["email"];
        const phoneNumber = contact.properties["phone"];
        const title = contact.properties["jobtitle"];

        res.push({
          id: contact.id,
          firstName: firstName,
          lastName: lastName,
          email: email,
          phoneNumber: phoneNumber,
          title: title,
        });
      }

      const finalRes = res.map((result) => ({
        ...result,
        accountId: associationsResponse.results
          .filter((association) => association._from.id === result.id)
          .flatMap((association) => association.to)
          .map((company) => company.id)[0],
      }));

      yield finalRes;
    }
  }

  async *getLeads(
    startDate: Date,
    endDate: Date,
    doFullDataPull: boolean = true,
    limit: number = 200,
  ): AsyncGenerator<
    {
      id: string;
      accountId?: string | null;
      title?: string | null;
      firstName?: string | null;
      lastName?: string | null;
      email?: string | null;
      phoneNumber?: string | null;
      lifecycleStage?: LifecycleStage;
      linkedInProfileLink?: string | null;
      utmParams: {};
      createDate?: Date | null;
      campaignGroupId?: string | null;
      campaignId?: string | null;
      companyName?: string | null;
      lastModifiedDate?: Date | null;
      creativeId?: string | null;
      pageTracking: {};
    }[]
  > {
    // const res =
    //   await this.hubspotClient.crm.properties.coreApi.getAll("contact");

    // res.results.forEach((result) => {
    //   console.log("RESULT NAME", result.name);
    //   console.log("RESULT LABEL", result.label);

    //   if (result.name.includes("utm")) {
    //     console.log("result", result);
    //   }
    // });

    const contactsGenerator = this.leadsSearchApi(
      doFullDataPull,
      [
        "firstname",
        "lastname",
        "email",
        "phone",
        "jobtitle",
        "lifecyclestage",
        "company", // company_name
        "createdate", // when contact was created in db
        "linkedin_profile_link",
        "hs_linkedin_url", // use vs linkedin profile link to determine url
        "utm_source",
        "utm_content",
        "utm_term",
        "utm_campaign",
        "hs_analytics_last_url",
        "hs_analytics_first_url",
        "lastmodifieddate",
      ],

      limit,
      startDate,
    );

    for await (const contacts of contactsGenerator) {
      const res: {
        id: string;
        accountId?: string | null;
        title?: string | null;
        firstName?: string | null;
        lastName?: string | null;
        email?: string | null;
        phoneNumber?: string | null;
        lifecycleStage?: LifecycleStage;
        linkedInProfileLink?: string | null;
        utmParams: {};
        createDate?: Date | null;
        campaignGroupId?: string | null;
        campaignId?: string | null;
        creativeId?: string | null;
        companyName?: string | null;
        lastModifiedDate?: Date | null;
        pageTracking: {};
      }[] = [];

      console.log("[hubspotProvider] - got contacts");
      // Note, these might of changed, refer to docs
      // https://docs.google.com/document/d/17cldABOaXoTTnGmwWWFZCu3TxU3RPkgj4J2p0ZmVF-Y/edit?tab=t.0
      // utm_source=Linkedin_Kalos&
      // utm_content={{CAMPAIGN_GROUP_ID}}&
      // utm_campaign={{CAMPAIGN_ID}}&
      // utm_term={{CREATIVE_ID}}
      for (const contact of contacts) {
        const firstName = contact.properties["firstname"];
        const lastName = contact.properties["lastname"];
        const email = contact.properties["email"];
        const phoneNumber = contact.properties["phone"];
        const title = contact.properties["jobtitle"];
        let lifecycleStage = contact.properties[
          "lifecyclestage"
        ] as LifecycleStage;
        const linkedInProfileLink =
          contact.properties["linkedin_profile_link"] ??
          contact.properties["hs_linkedin_url"];
        const createDate = contact.properties["createdate"];
        const companyName = contact.properties["company"];
        const lastModifiedDate = contact.properties["lastmodifieddate"]
          ? new Date(contact.properties["lastmodifieddate"])
          : undefined;

        const utmSource = contact.properties["utm_source"];
        const utmCampaign = contact.properties["utm_campaign"];
        const utmTerm = contact.properties["utm_term"];
        const utmMedium = contact.properties["utm_medium"];
        const utmContent = contact.properties["utm_content"];
        const utmChannel = contact.properties["utm_channel"];

        const lastPageSeen = contact.properties["hs_analytics_last_url"];
        const firstPageSeen = contact.properties["hs_analytics_first_url"];

        let utmParams: {
          utm_source?: string;
          utm_content?: string;
          utm_campaign?: string;
          utm_medium?: string;
          utm_term?: string;
          utm_channel?: string; // used sometimes in kalos
        } = {};

        const pageTracking: Record<string, string> = {};

        if (lastPageSeen) pageTracking["lastPageSeen"] = lastPageSeen;
        if (firstPageSeen) pageTracking["firstPageSeen"] = firstPageSeen;

        // UTM Data Extraction
        // 1. start with utm fields - sometimes not reliable if not setup
        if (utmSource) utmParams.utm_source = utmSource;
        if (utmTerm) utmParams.utm_term = utmTerm;
        if (utmContent) utmParams.utm_content = utmContent;
        if (utmCampaign) utmParams.utm_campaign = utmCampaign;
        if (utmChannel) utmParams.utm_channel = utmChannel;
        if (utmMedium) utmParams.utm_medium = utmMedium;

        // 2. Override with values from lastPageSeen (if any)
        utmParams = { ...utmParams, ...this.extractUtmFromUrl(lastPageSeen) };

        // 3. Override with values from firstPageSeen (highest precedence)
        utmParams = { ...utmParams, ...this.extractUtmFromUrl(firstPageSeen) };

        if (
          ![
            "subscriber",
            "lead",
            "marketingqualifiedlead",
            "salesqualifiedlead",
            "opportunity",
            "customer",
            "evangelist",
            "other",
          ].includes(lifecycleStage || "")
        ) {
          lifecycleStage = null;
        }

        let customUtmParamsFlag: boolean | undefined = false;

        try {
          customUtmParamsFlag = await this.posthogClient.isFeatureEnabled(
            "use-hubspot-custom-utm",
            "arbitrary-string",
            {
              personProperties: {
                organizationId: this.organizationId.toString(),
              },
            },
          );
        } catch (error) {
          console.log("Error retrieving posthog utm params", error);
        }

        if (customUtmParamsFlag) {
          res.push({
            id: contact.id,
            firstName: firstName,
            lastName: lastName,
            email: email,
            phoneNumber: phoneNumber,
            title: title,
            lifecycleStage: lifecycleStage,
            linkedInProfileLink: linkedInProfileLink,
            utmParams,
            createDate: createDate ? new Date(createDate) : undefined,
            campaignGroupId: "",
            creativeId: utmParams.utm_content,
            campaignId: utmParams.utm_campaign,
            companyName: companyName,
            lastModifiedDate: lastModifiedDate,
            pageTracking: pageTracking,
          });
        } else {
          res.push({
            id: contact.id,
            firstName: firstName,
            lastName: lastName,
            email: email,
            phoneNumber: phoneNumber,
            title: title,
            lifecycleStage: lifecycleStage,
            linkedInProfileLink: linkedInProfileLink,
            utmParams,
            createDate: createDate ? new Date(createDate) : undefined,
            campaignGroupId: utmParams.utm_content,
            creativeId: utmParams.utm_term,
            campaignId: utmParams.utm_campaign,
            companyName: companyName,
            lastModifiedDate: lastModifiedDate,
            pageTracking: pageTracking,
          });
        }
      }
      const finalRes = res;
      // Don't send too many requests
      await new Promise((resolve) => setTimeout(resolve, 1000));

      yield finalRes;
    }
  }
  getCrmUsers(
    startDate: Date,
    endDate: Date,
    limit?: number,
  ): Promise<
    {
      id: string;
      name: string;
      firstName?: string | null;
      lastName?: string | null;
      email: string;
    }[]
  > {
    throw new Error("Method not implemented.");
  }

  static async createFactory(
    hubspotCredentials: HubspotCredentials,
  ): Promise<HubspotProvider> {
    const clientSecret = process.env.HUBSPOT_CLIENT_SECRET;

    if (!clientSecret) {
      throw new Error("No client secret found for organization");
    }
    const clientId = process.env.HUBSPOT_CLIENT_ID;
    if (!clientId) {
      throw new Error("No client id found for organization");
    }
    const hubspotClient = new Client();
    const tokensApiResponse = await hubspotClient.oauth.tokensApi.create(
      "refresh_token",
      undefined,
      undefined,
      clientId,
      clientSecret,
      hubspotCredentials.refreshToken,
    );
    hubspotClient.setAccessToken(tokensApiResponse.accessToken);
    return new HubspotProvider(
      hubspotClient,
      hubspotCredentials.organizationId,
    );
  }

  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  async doThing() {
    const res = await this.hubspotClient.crm.companies.searchApi.doSearch({
      properties: ["name"],
      filterGroups: [
        {
          filters: [
            {
              propertyName: "hs_object_id",
              value: "20927655270",
              operator: FilterOperatorEnum.Eq,
            },
          ],
        },
      ],
    });
    return res;
  }
}
