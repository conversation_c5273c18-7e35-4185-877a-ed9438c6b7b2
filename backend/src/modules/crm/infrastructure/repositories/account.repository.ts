import { and, eq, inArray, sql } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { accountTable } from "../../../../database/schemas/crm/account.table";
import { IAccountRepository } from "../../application/interfaces/infrastructure/repositories/account.repository.interface";
import { Account } from "../../domain/entities/account";

export const accountRepository: IAccountRepository = {
  async createOne(account: Account, tx?: Transaction): Promise<Account> {
    const invoker = tx ?? db;
    await invoker.insert(accountTable).values(account);
    return account;
  },

  async createOrUpdateIfExistsMany(
    accounts: Account[],
    tx?: Transaction,
  ): Promise<Account[]> {
    if (accounts.length === 0) {
      return [];
    }
    const invoker = tx ?? db;
    const createdOrUpdatedAccounts = await invoker
      .insert(accountTable)
      .values(accounts)
      .onConflictDoUpdate({
        target: [accountTable.crmId, accountTable.organizationId],
        set: {
          name: sql`EXCLUDED.name`,
          vertical: sql`EXCLUDED.vertical`,
          numberOfEmployees: sql`EXCLUDED.number_of_employees`,
          annualRevenue: sql`EXCLUDED.annual_revenue`,
          description: sql`EXCLUDED.description`,
          website: sql`EXCLUDED.website`,
          subVertical: sql`EXCLUDED.sub_vertical`,
          ownerId: sql`EXCLUDED.owner_id`,
          updatedAt: sql`now()`,
        },
      })
      .returning();
    return createdOrUpdatedAccounts;
  },

  async getAccountById(id: string, tx?: Transaction): Promise<Account | null> {
    const invoker = tx ?? db;
    const account = await invoker
      .select()
      .from(accountTable)
      .where(eq(accountTable.id, id));
    if (!account[0]) {
      return null;
    }
    return Account(account[0]);
  },

  async getManyByCrmIds(
    crmIds: string[],
    organizationId: number,
    tx?: Transaction,
  ): Promise<Account[]> {
    if (crmIds.length === 0) {
      return [];
    }
    const invoker = tx ?? db;
    const accounts = await invoker
      .select()
      .from(accountTable)
      .where(
        and(
          eq(accountTable.organizationId, organizationId),
          inArray(accountTable.crmId, crmIds),
        ),
      );
    return accounts.map((account) => Account(account));
  },

  async getVerticalsForOrganization(
    organizationId: number,
    tx?: Transaction,
  ): Promise<string[]> {
    const invoker = tx ?? db;
    const verticals = await invoker
      .selectDistinct({
        vertical: accountTable.vertical,
      })
      .from(accountTable)
      .where(eq(accountTable.organizationId, organizationId))
      .groupBy(accountTable.vertical);
    return verticals
      .map((vertical) => vertical.vertical)
      .filter((each) => each !== null)
      .filter((each) => each !== "")
      .filter((each) => each.length > 0);
  },
};
