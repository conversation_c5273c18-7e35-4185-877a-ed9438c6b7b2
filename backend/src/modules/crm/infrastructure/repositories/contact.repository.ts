import { and, count, eq, gte, inArray, isNot<PERSON>ull, lte, sql } from "drizzle-orm";

import { segmentHandlers } from "@kalos/database/handlers/segment";

import { db, Transaction } from "../../../../database/db";
import { linkedInSponsoredCreativeTable } from "../../../../database/schemas/advertising/linkedInSponsoredCreative.table";
import { jobFunctionTable } from "../../../../database/schemas/core/jobFunction.table";
import { jobSeniorityTable } from "../../../../database/schemas/core/jobSeniority.table";
import { contactTable } from "../../../../database/schemas/crm/contact.table";
import { Segment } from "../../../core/domain/entites/segment";
import { JobFunction } from "../../../core/domain/valueObjects/jobFunction";
import { JobSeniority } from "../../../core/domain/valueObjects/jobSenitory";
import { createUuid } from "../../../core/utils/uuid";
import { IContactRepository } from "../../application/interfaces/infrastructure/repositories/contact.repository.interface";
import { Contact } from "../../domain/entities/contact";

export const contactRepository: IContactRepository = {
  async createOne(contact: Contact, tx?: Transaction): Promise<Contact> {
    const invoker = tx ?? db;
    await invoker.insert(contactTable).values(contact);
    return contact;
  },

  // uses crmId + orgId as unique
  async createOrUpdateIfExistsMany(
    contacts: Contact[],
    tx?: Transaction,
  ): Promise<Contact[]> {
    if (contacts.length === 0) {
      return [];
    }

    const invoker = tx ?? db;
    await invoker
      .insert(contactTable)
      .values(contacts)
      .onConflictDoUpdate({
        target: [contactTable.crmId, contactTable.organizationId],
        set: {
          firstName: sql`EXCLUDED.first_name`,
          lastName: sql`EXCLUDED.last_name`,
          email: sql`EXCLUDED.email`,
          phoneNumber: sql`EXCLUDED.phone_number`,
          title: sql`EXCLUDED.title`,
          accountId: sql`EXCLUDED.account_id`,
          jobFunctionId: sql`EXCLUDED.job_function_id`,
          jobSeniorityId: sql`EXCLUDED.job_seniority_id`,
          linkedinCampaignGroupUrn: sql`EXCLUDED.linkedin_campaign_group_urn`,
          utmParams: sql`EXCLUDED.utm_params`,
          lifecycleStage: sql`EXCLUDED.lifecycle_stage`,
          leadCreatedAt: sql`EXCLUDED.lead_created_at`,
          source: sql`EXCLUDED.source`,
          companyName: sql`EXCLUDED.company_name`,
          pageTracking: sql`EXCLUDED.page_tracking`,
          updatedAt: sql`now()`,
        },
      });
    return contacts.map((contact) => Contact(contact));
  },

  // uses email + orgId as unique
  async createOrUpdateIfExistsEmailUniqueMany(
    contacts: Contact[],
    tx?: Transaction,
  ): Promise<Contact[]> {
    if (contacts.length === 0) {
      return [];
    }

    const invoker = tx ?? db;
    await invoker
      .insert(contactTable)
      .values(contacts)
      .onConflictDoUpdate({
        target: [contactTable.email, contactTable.organizationId],
        set: {
          firstName: sql`EXCLUDED.first_name`,
          lastName: sql`EXCLUDED.last_name`,
          email: sql`EXCLUDED.email`,
          phoneNumber: sql`EXCLUDED.phone_number`,
          title: sql`EXCLUDED.title`,
          accountId: sql`EXCLUDED.account_id`,
          jobFunctionId: sql`EXCLUDED.job_function_id`,
          jobSeniorityId: sql`EXCLUDED.job_seniority_id`,
          linkedinCampaignGroupUrn: sql`EXCLUDED.linkedin_campaign_group_urn`,
          linkedinSponsoredCreativeUrn: sql`EXCLUDED.linkedin_sponsored_creative_urn`,
          utmParams: sql`EXCLUDED.utm_params`,
          lifecycleStage: sql`EXCLUDED.lifecycle_stage`,
          // leadCreatedAt: sql`EXCLUDED.lead_created_at`, only create this for a lead / don't udpate
          source: sql`EXCLUDED.source`,
          companyName: sql`EXCLUDED.company_name`,
          pageTracking: sql`EXCLUDED.page_tracking`,
          updatedAt: sql`now()`,
        },
      });
    return contacts.map((contact) => Contact(contact));
  },
  async getContactById(id: string, tx?: Transaction): Promise<Contact | null> {
    const invoker = tx ?? db;
    const contact = await invoker
      .select()
      .from(contactTable)
      .where(eq(contactTable.id, id));
    if (!contact[0]) {
      return null;
    }
    return Contact({
      ...contact[0],
      utmParams: contact[0].utmParams
        ? (contact[0].utmParams as Record<string, string>)
        : null,
      pageTracking: contact[0].pageTracking
        ? (contact[0].pageTracking as Record<string, string>)
        : null,
    });
  },

  /**
   * Get contacts by their CRM IDs and organization ID, returning only the necessary ID mapping
   * @param crmIds - Array of CRM IDs to look up
   * @param organizationId - Organization ID
   * @param tx - Optional transaction
   * @returns Array of objects with id and crmId properties
   */
  async getManyByCrmIds(
    crmIds: string[],
    organizationId: number,
    tx?: Transaction,
  ): Promise<{ id: string; crmId: string }[]> {
    if (crmIds.length === 0) {
      return [];
    }
    const invoker = tx ?? db;
    const contacts = await invoker
      .select({
        id: contactTable.id,
        crmId: contactTable.crmId,
      })
      .from(contactTable)
      .where(
        and(
          eq(contactTable.organizationId, organizationId),
          inArray(contactTable.crmId, crmIds),
        ),
      );
    return contacts.filter((contact) => contact.crmId !== null) as {
      id: string;
      crmId: string;
    }[];
  },

  async updateJobFunctionAndSeniority(
    input: {
      contactId: string;
      jobFunction: JobFunction | null;
      jobSeniority: JobSeniority | null;
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const [jobFunctions, jobSeniorities] = await Promise.all([
      invoker.select().from(jobFunctionTable),
      invoker.select().from(jobSeniorityTable),
    ]);

    const jobFunctionId =
      input.jobFunction !== null
        ? jobFunctions.find(
            (jobFunction) => jobFunction.name === input.jobFunction,
          )?.id
        : null;
    const jobSeniorityId =
      input.jobSeniority !== null
        ? jobSeniorities.find(
            (jobSeniority) => jobSeniority.name === input.jobSeniority,
          )?.id
        : null;

    if (jobFunctionId == undefined || jobSeniorityId == undefined) {
      throw new Error("Job function or seniority not found");
    }

    await invoker
      .update(contactTable)
      .set({
        jobFunctionId: jobFunctionId,
        jobSeniorityId: jobSeniorityId,
        updatedAt: sql`now()`,
      })
      .where(eq(contactTable.id, input.contactId));
  },

  async getContactsWithJobFunction(
    jobFunction: string,
    organizationId: number,
    tx?: Transaction,
  ): Promise<Contact[]> {
    const invoker = tx ?? db;
    const contacts = await invoker
      .select()
      .from(contactTable)
      .where(
        and(
          eq(contactTable.organizationId, organizationId),
          eq(jobFunctionTable.name, jobFunction),
        ),
      )
      .innerJoin(
        jobFunctionTable,
        eq(contactTable.jobFunctionId, jobFunctionTable.id),
      );

    return contacts.map((contact) =>
      Contact({
        ...contact.contact,
        utmParams: contact.contact.utmParams
          ? (contact.contact.utmParams as Record<string, string>)
          : null,
        pageTracking: contact.contact.pageTracking
          ? (contact.contact.pageTracking as Record<string, string>)
          : null,
      }),
    );
  },

  getSelectLeadsForDateRange(
    organization_id: number,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;

    const crmLeadsColumns = {
      id: contactTable.id,
      source: sql`${contactTable.source}::text`.as("source"), // soure might be null
      linkedinCampaignUrn: contactTable.linkedinCampaignGroupUrn,
      leadCreatedAt: sql<Date>`${contactTable.leadCreatedAt}`.as(
        "leadCreatedAt",
      ),
      firstName: contactTable.firstName,
      lastName: contactTable.lastName,
      workEmail: contactTable.email, // or a separate field if you store both
      companyName: contactTable.companyName,
      jobTitle: contactTable.title,
      linkedinProfileLink: contactTable.linkedinProfileLink,
      // status:contactTable.lifecycleStage
    };

    const crmFilters =
      startDateFilter && endDateFilter
        ? and(
            gte(contactTable.leadCreatedAt, startDateFilter),
            lte(contactTable.leadCreatedAt, endDateFilter),
            eq(contactTable.organizationId, organization_id),
            inArray(contactTable.lifecycleStage, [
              "lead",
              "salesqualifiedlead",
              "marketingqualifiedlead",
              "opportunity",
            ]),
          )
        : and(
            eq(contactTable.organizationId, organization_id),
            inArray(contactTable.lifecycleStage, [
              "lead",
              "salesqualifiedlead",
              "marketingqualifiedlead",
              "opportunity",
            ]),
          );

    const crmLeadsQuery = invoker
      .select(crmLeadsColumns)
      .from(contactTable)
      .where(crmFilters);

    return crmLeadsQuery;
  },

  async getOneForOrganizationId(
    organizationId: number,
    tx?: Transaction,
  ): Promise<Contact | null> {
    const invoker = tx ?? db;
    const contacts = await invoker
      .select()
      .from(contactTable)
      .where(eq(contactTable.organizationId, organizationId));

    if (!contacts[0]) {
      return null;
    }

    return Contact({
      ...contacts[0],
      utmParams: contacts[0].utmParams
        ? (contacts[0].utmParams as Record<string, string>)
        : null,
      pageTracking: contacts[0].pageTracking
        ? (contacts[0].pageTracking as Record<string, string>)
        : null,
    });
  },

  async getLeadsMetrics(
    organization_id: number,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: Transaction,
  ): Promise<{
    totalLeads: number;
  }> {
    const invoker = tx ?? db;

    const crmFilters =
      startDateFilter && endDateFilter
        ? and(
            gte(contactTable.leadCreatedAt, startDateFilter),
            lte(contactTable.leadCreatedAt, endDateFilter),
            eq(contactTable.organizationId, organization_id),
            inArray(contactTable.lifecycleStage, [
              "lead",
              "salesqualifiedlead",
              "marketingqualifiedlead",
            ]),
          )
        : and(
            eq(contactTable.organizationId, organization_id),
            inArray(contactTable.lifecycleStage, [
              "lead",
              "salesqualifiedlead",
              "marketingqualifiedlead",
            ]),
          );

    const totalLeads = await invoker
      .select({ count: count() })
      .from(contactTable)
      .where(crmFilters);

    return {
      totalLeads: totalLeads[0]?.count || 0,
    };
  },
};
