import { IAdSegmentValuePropRepository } from "../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { LinkedInService } from "../../../../infrastructure/services/linkedIn.service";
import { MapLinkedInStateInputToSponsoredCreativesService } from "../../../../linkedInStateOrchestrator/application/services/mapLinkedInStateInputToSponsoredCreatives.service";
import { AbTestRoundDataDto } from "../../dtos/abTestRoundData.dto";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { GetAbTestRoundDataCommand } from "./getAbTestRoundData.command.interface";

export class GetAbTestRoundDataQueryHandler {
  constructor(
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly dataProvider: DataProviderService,
    private readonly linkedInService: LinkedInService,
    private readonly mapLinkedInStateInputToSponsoredCreativesService: MapLinkedInStateInputToSponsoredCreativesService,
    private readonly adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository,
    private readonly adSegmentValuePropRepository: IAdSegmentValuePropRepository,
  ) {}
  async execute(
    command: GetAbTestRoundDataCommand,
  ): Promise<AbTestRoundDataDto | null> {
    const abTestRound = await this.abTestRoundRepository.getOne(
      command.abTestRoundId,
      command.type,
      command.tx,
    );
    if (!abTestRound) {
      return null;
    }

    const linkedInStateOrchestratorInput =
      await this.dataProvider.getLinkedInStateOrchestratorInput({
        adSegmentId: command.adSegmentId,
        abTestType: command.type,
        currentBestVariantId: abTestRound.currentBestId,
        contenderVariantId: abTestRound.contenderId,
      });

    if (linkedInStateOrchestratorInput.isErr()) {
      return null;
    }

    if (
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_CONTENT"
    ) {
      const mappedSponsoredCreatives =
        await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredContent(
          linkedInStateOrchestratorInput.value,
        );

      const metrics = await this.linkedInService.getAnalyticsForCreatives({
        sponsoredCreativeUrns: mappedSponsoredCreatives.map(
          (creative) =>
            creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
        ),
        startDate: new Date("2024-01-01"),
        endDate: new Date("2025-01-01"),
        timeGranularity: "ALL",
      });

      const mappedSponsoredCreativesWithMetrics = mappedSponsoredCreatives.map(
        (creative) => {
          const metric = metrics.find(
            (metric) =>
              metric.sponsoredCreatieUrn ===
              creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
          );
          if (!metric) {
            throw new Error("Metrics not found");
          }
          return {
            ...creative,
            metrics: metric,
          };
        },
      );

      let currentBestMetrics = null;
      let contenderMetrics = null;

      if (command.type == "audience") {
        currentBestMetrics = mappedSponsoredCreativesWithMetrics.find(
          (creative) => creative.audienceId == abTestRound.currentBestId,
        );
        contenderMetrics = mappedSponsoredCreativesWithMetrics.find(
          (creative) => creative.audienceId == abTestRound.contenderId,
        );
      } else if (command.type == "valueProp") {
        currentBestMetrics = mappedSponsoredCreativesWithMetrics.find(
          (creative) => creative.valuePropId == abTestRound.currentBestId,
        );
        contenderMetrics = mappedSponsoredCreativesWithMetrics.find(
          (creative) => creative.valuePropId == abTestRound.contenderId,
        );
      } else if (command.type == "creative") {
        currentBestMetrics = mappedSponsoredCreativesWithMetrics.find(
          (creative) =>
            creative.adProgramCreativeId == abTestRound.currentBestId,
        );
        contenderMetrics = mappedSponsoredCreativesWithMetrics.find(
          (creative) => creative.adProgramCreativeId == abTestRound.contenderId,
        );
      }

      if (!currentBestMetrics || !contenderMetrics) {
        return null;
      }

      const [
        currentBestAdProgramCreative,
        contenderAdProgramCreative,
        currentBestValueProp,
        contenderValueProp,
      ] = await Promise.all([
        this.adProgramCreativeRepository.getOne(
          currentBestMetrics.adProgramCreativeId,
        ),
        this.adProgramCreativeRepository.getOne(
          contenderMetrics.adProgramCreativeId,
        ),
        this.adSegmentValuePropRepository.getOne(
          currentBestMetrics.valuePropId,
          "ACTIVE",
        ),
        this.adSegmentValuePropRepository.getOne(
          contenderMetrics.valuePropId,
          "ACTIVE",
        ),
      ]);

      return {
        currentBest: {
          sponsoredCreativeId: abTestRound.currentBestId,
          varientId: abTestRound.currentBestId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_CONTENT",
            audienceType: currentBestMetrics.audienceId,
            valuePropType: currentBestMetrics.valuePropId,
            creativeType: currentBestMetrics.adProgramCreativeId,
            socialPostBodyCopyType: currentBestMetrics.socialPostBodyCopyType,
            socialPostCallToActionType:
              currentBestMetrics.socialPostCallToActionType,
          },
          metrics: currentBestMetrics.metrics,
        },
        contender: {
          sponsoredCreativeId: abTestRound.contenderId,
          varientId: abTestRound.contenderId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_CONTENT",
            audienceType: contenderMetrics.audienceId,
            valuePropType: contenderMetrics.valuePropId,
            creativeType: contenderMetrics.adProgramCreativeId,
            socialPostBodyCopyType: contenderMetrics.socialPostBodyCopyType,
            socialPostCallToActionType:
              contenderMetrics.socialPostCallToActionType,
          },
          metrics: contenderMetrics.metrics,
        },
      };
    } else if (
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_INMAIL"
    ) {
      const mappedSponsoredCreatives =
        await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredInmail(
          linkedInStateOrchestratorInput.value,
        );
    }
  }
}
