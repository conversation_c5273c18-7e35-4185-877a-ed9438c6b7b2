import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { GetAbTestDataCommand } from "./getAbTestData.query.interface";

export class GetAbTestDataQueryHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
  ) {}
  async execute(command: GetAbTestDataCommand) {
    const abTest = await this.abTestRepository.getOne(
      command.stageId,
      command.type,
      command.tx,
    );
    if (!abTest) {
      return null;
    }

    const abTestRounds = await this.abTestRoundRepository.getAllForAbTest(
      abTest.stageId,
      command.type,
      command.tx,
    );

    return {
      abTest,
      rounds: abTestRounds,
    };
  }
}
