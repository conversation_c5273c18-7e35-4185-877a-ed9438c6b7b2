import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { SegmentService } from "../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../core/infrastructure/repositories/segment.repository";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { createNewVariantsDtoSchema } from "../../application/dtos/controllerDtos/adSegment/createNewVariants.dto";
import { getAllAdSegmentsForAdProgramRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegment/getAllAdSegmentsForAdProgram.dto";
import { getAllAdSegmentsForAdProgramsRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegment/getAllAdSegmentsForAdPrograms.dto";
import { getAllAdSegmentsForSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegment/getAllAdSegmentsForSegment.dto";
import { getSegmentDetailWithAdsRequestDto } from "../../application/dtos/controllerDtos/adSegment/getSegmentDetailWithAds.dto";
import { setAdSegmentsForAdProgramRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegment/setAdSegmentsForAdProgram.dto";
import { deployLinkedInAdProgramRequestDto } from "../../application/dtos/controllerDtos/linkedInAdProgram/deployLinkedInAdProgram.dto";
import { getAllAdSegmentsForAdProgramUseCase } from "../../application/useCase/adSegment/getAllAdSegmentsForAdProgram.useCase";
import { getAllAdSegmentsForAdProgramsUseCase } from "../../application/useCase/adSegment/getAllAdSegmentsForAdPrograms.useCase";
import { getAllAdSegmentsForSegmentUseCase } from "../../application/useCase/adSegment/getAllAdSegmentsForSegment.useCase";
import { GetSegmentDetailWithAdsUseCase } from "../../application/useCase/adSegment/getSegmentDetailWithAds.useCase";
import { setAdSegmentsForAdProgramUseCase } from "../../application/useCase/adSegment/setAdSegmentsForAdProgram.useCase";
import { AdAudienceService } from "../../domain/services/adAudience.service";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "../../domain/services/conversationCopy.service";
import { ConversationMessageCopyService } from "../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { LinkedInAdSegmentSocialPostBaseCopyService } from "../../domain/services/linkedInAdSegmentSocialPostBaseCopy.service";
import { LinkedInConversationBaseCopyService } from "../../domain/services/linkedInConversationBaseCopy.service";
import { SegmentAudienceTargetPrefabService } from "../../domain/services/segmentAudiencePrefab.service";
import { SocialPostAdCopyService } from "../../domain/services/socialPostAdCopy.service";
import { AdSegmentSelectedConversationSubjectTypeRepository } from "../../infrastructure/repositories/adSegmentSelectedConversationSubjectType.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { AdSegmentValuePropCreativeRepository } from "../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { ConversationCallToActionCopyRepository } from "../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationCopyRepository } from "../../infrastructure/repositories/conversationCopy.repository";
import { ConversationMessageCopyRepository } from "../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInAdSegmentConversationBaseCopyRepository } from "../../infrastructure/repositories/linkedInAdSegmentConversationPostBaseCopy.repository";
import { LinkedInAdSegmentSocialPostBaseCopyRepository } from "../../infrastructure/repositories/linkedInAdSegmentSocialPostBaseCopy.repository";
import { SegmentAudiencePrefabRepository } from "../../infrastructure/repositories/segmentAudienceTargetPrefabRepository";
import { SocialPostCallToActionCopyRepository } from "../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { SocialPostCopyRepository } from "../../infrastructure/repositories/socialPostCopy.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const adSegmentController = {
  setAdSegmentsForAdProgram: organizationRoute
    .input(setAdSegmentsForAdProgramRequestDtoSchema)
    .mutation(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();
      const adAudienceRepository = new LinkedInAdAudienceRepository();
      const adAudienceService = new AdAudienceService(adAudienceRepository);
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegmentService = new AdSegmentService(adSegmentRepository);
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adProgramService = new LinkedInAdProgramService(
        adProgramRepository,
      );
      const segmentService = new SegmentService(segmentRepository);
      const segmentAudiencePrefabRepository =
        new SegmentAudiencePrefabRepository();
      const segmentAudiencePrefabService =
        new SegmentAudienceTargetPrefabService(segmentAudiencePrefabRepository);
      const adSegmentValuePropRepository = new AdSegmentValuePropRepository();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        adSegmentValuePropRepository,
      );
      const linkedInAdSegmentSocialPostBaseCopyRepository =
        new LinkedInAdSegmentSocialPostBaseCopyRepository();
      const linkedInAdSegmentSocialPostBaseCopyService =
        new LinkedInAdSegmentSocialPostBaseCopyService(
          linkedInAdSegmentSocialPostBaseCopyRepository,
        );

      const socialPostAdCopyRepository = new SocialPostCopyRepository();
      const socialPostAdCopyService = new SocialPostAdCopyService(
        socialPostAdCopyRepository,
      );

      const conversationBaseCopyRepository =
        new LinkedInAdSegmentConversationBaseCopyRepository();
      const conversationBaseCopyService =
        new LinkedInConversationBaseCopyService(conversationBaseCopyRepository);

      const conversationCopyRepository = new ConversationCopyRepository();
      const conversationCopyService = new ConversationCopySerivce(
        conversationCopyRepository,
      );

      const conversationSubjectCopyRepository =
        new ConversationSubjectCopyRepository();
      const conversationSubjectCopyService = new ConversationSubjectCopyService(
        conversationSubjectCopyRepository,
      );

      const conversationMessageCopyRepository =
        new ConversationMessageCopyRepository();
      const conversationMessageCopyService = new ConversationMessageCopyService(
        conversationMessageCopyRepository,
      );

      const conversationCallToActionCopyRepository =
        new ConversationCallToActionCopyRepository();
      const conversationCallToActionCopyService =
        new ConversationCallToActionCopyService(
          conversationCallToActionCopyRepository,
        );

      const adSegmentSelectedConversationSubjectTypeRepository =
        new AdSegmentSelectedConversationSubjectTypeRepository();
      const adSegmentSelectedConversationSubjectTypeService =
        new AdSegmentSelectedConversationSubjectTypeService(
          adSegmentSelectedConversationSubjectTypeRepository,
        );

      const adSegmentValuePropCreativeRepository =
        new AdSegmentValuePropCreativeRepository();

      const socialPostCallToActionCopyRepository =
        new SocialPostCallToActionCopyRepository();

      const result = await transactionManager.startTransaction(async (tx) => {
        const { created } = await setAdSegmentsForAdProgramUseCase(input, {
          adAudienceService: adAudienceService,
          adSegmentService: adSegmentService,
          linkedInAdProgramService: adProgramService,
          segmentService: segmentService,
          organizationId: ctx.organizationId,
          segmentAudiencePrefabService: segmentAudiencePrefabService,
          adSegmentValuePropService: adSegmentValuePropService,
          socialPostAdCopyService: socialPostAdCopyService,
          linkedInAdSegmentSocialPostBaseCopyService:
            linkedInAdSegmentSocialPostBaseCopyService,
          conversationBaseCopyService: conversationBaseCopyService,
          conversationCopyService: conversationCopyService,
          conversationSubjectCopyService: conversationSubjectCopyService,
          conversationMessageCopyService: conversationMessageCopyService,
          conversationCallToActionCopyService:
            conversationCallToActionCopyService,
          adSegmentSelectedConversationSubjectTypeService:
            adSegmentSelectedConversationSubjectTypeService,
          adSegmentValuePropCreativeRepository:
            adSegmentValuePropCreativeRepository,
          socialPostCallToActionCopyRepository:
            socialPostCallToActionCopyRepository,
          tx,
        });
        return { created };
      });

      const { created } = result;

      const adProgram = await adProgramService.getOne(input.adProgramId);
      if (adProgram?.adFormat.type == "SPONSORED_CONTENT") {
        for (const eachCreated of created) {
          await advertisingInngestClient.send({
            name: "linkedin/create-ad-segment-base-social-post-copy",
            data: {
              adSegmentId: eachCreated.id,
              organizationId: ctx.organizationId,
            },
          });
        }
      } else if (adProgram?.adFormat.type == "SPONSORED_INMAIL") {
        for (const eachCreated of created) {
          await advertisingInngestClient.send({
            name: "linkedin/create-ad-segment-base-conversation-copy",
            data: {
              adSegmentId: eachCreated.id,
              organizationId: ctx.organizationId,
            },
          });
        }
      }
    }),
  getAllAdSegmentsForAdProgram: organizationRoute
    .input(getAllAdSegmentsForAdProgramRequestDtoSchema)
    .query(async ({ input, ctx }) => {
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegmentService = new AdSegmentService(adSegmentRepository);
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adProgramService = new LinkedInAdProgramService(
        adProgramRepository,
      );
      return getAllAdSegmentsForAdProgramUseCase(input, {
        adProgramService: adProgramService,
        adSegmentService: adSegmentService,
        organizationId: ctx.organizationId,
      });
    }),

  getSegmentDetailWithAds: organizationRoute
    .input(getSegmentDetailWithAdsRequestDto)
    .query(async ({ input, ctx }) => {
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegmentService = new AdSegmentService(adSegmentRepository);
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adProgramService = new LinkedInAdProgramService(
        adProgramRepository,
      );

      const useCase = new GetSegmentDetailWithAdsUseCase({
        adSegmentService,
        linkedInAdProgramService: adProgramService,
        segmentService: new SegmentService(segmentRepository),
      });

      return useCase.execute({
        segmentId: input.segmentId,
        organizationId: ctx.organizationId,
      });
    }),

  getAllAdSegmentsForAdPrograms: organizationRoute
    .input(getAllAdSegmentsForAdProgramsRequestDtoSchema)
    .query(async ({ input, ctx }) => {
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegmentService = new AdSegmentService(adSegmentRepository);
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adProgramService = new LinkedInAdProgramService(
        adProgramRepository,
      );

      return getAllAdSegmentsForAdProgramsUseCase(input, {
        adProgramService: adProgramService,
        adSegmentService: adSegmentService,
        organizationId: ctx.organizationId,
      });
    }),

  getAllAdSegmentsForSegment: organizationRoute
    .input(getAllAdSegmentsForSegmentRequestDto)
    .query(async ({ input, ctx }) => {
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegmentService = new AdSegmentService(adSegmentRepository);
      const segmentService = new SegmentService(segmentRepository);

      return getAllAdSegmentsForSegmentUseCase(input, {
        adSegmentService,
        segmentService,
        organizationId: ctx.organizationId,
      });
    }),

  getOne: organizationRoute
    .input(z.object({ adSegmentId: z.string() }))
    .query(async ({ input, ctx }) => {
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegmentService = new AdSegmentService(adSegmentRepository);
      return adSegmentService.getOne(input.adSegmentId);
    }),

  createNewVarients: organizationRoute
    .input(createNewVariantsDtoSchema)
    .mutation(async ({ input, ctx }) => {
      await advertisingInngestClient.send({
        name: "linkedin/createNewVarients",
        data: {
          input: input,
          ctx: {
            organizationId: ctx.organizationId,
            userId: ctx.userId,
          },
        },
      });
    }),
};
