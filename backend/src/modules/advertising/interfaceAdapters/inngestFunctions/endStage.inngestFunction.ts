import { NonRetriableError } from "inngest";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { AbTestRepository } from "../../infrastructure/repositories/abTest.repository";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { StageRepository } from "../../infrastructure/repositories/stage.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

interface EventData {
  stageId: string;
}

export const endStage = advertisingInngestClient.createFunction(
  { id: "end-linked-in-stage" },
  { event: "linkedin/stage.end" },
  async ({ step, event, logger }) => {
    const eventData: EventData = event.data;
    const stage = await step.run("getStage", async () => {
      const stageRepository = new StageRepository();
      const stage = await stageRepository.getStage(eventData.stageId);
      return stage;
    });

    if (!stage) {
      throw new NonRetriableError("Stage not found");
    }

    await step.run("endStage", async () => {
      const stageRepository = new StageRepository();
      await stageRepository.updateStageStatus(stage.id, "FINISHED");
    });

    const orgAndAccountData = await step.run("getOrganizationId", async () => {
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adSegment = await adSegmentRepository.getOne(stage.adSegmentId);
      if (!adSegment) {
        throw new NonRetriableError("Ad segment not found");
      }
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adProgram = await adProgramRepository.getOne(
        adSegment.linkedInAdProgramId,
      );
      if (!adProgram) {
        throw new NonRetriableError("Ad program not found");
      }
      const linkedInAccountRepository = new LinkedInAdAccountRepository();
      const linkedInAccount = await linkedInAccountRepository.getOneById(
        adProgram.linkedInAdAccountId,
      );
      if (!linkedInAccount) {
        throw new NonRetriableError("LinkedIn account not found");
      }
      return {
        orgId: linkedInAccount.organizationId,
        linkedInAdAccountUrn: linkedInAccount.linkedInAdAccountUrn,
        adProgram: adProgram,
      };
    });

    // Steps to complete
    // Check if ending stage is audience
    // if it is, we will update budget for campaign in linkedin
    // we will do this by checking to see how many audiences existed
    // if 1, don't change budget
    // if more than 1 audiences, double daily budget
    if (stage.stageType === "audienceTest") {
      await step.run("updateBudgetIfStageIsAudience", async () => {
        // Check to see how many Audiences exist
        // Get Campaign Group Because it contains budget
        const adSegmentId = stage.adSegmentId;

        const linkedInAudienceRepository = new LinkedInAdAudienceRepository();

        // Campaign Group Audiences
        const adSegmentAudiences =
          await linkedInAudienceRepository.getAllForAdSegment(adSegmentId);

        let numOfAudiences = 0;

        adSegmentAudiences.forEach((aud) => {
          if (aud.toBeUsed) {
            numOfAudiences += 1;
          }
        });

        // only change budget if there is more than 1 audience
        if (numOfAudiences > 1) {
          // Get Campaign Group budget
          const linkedInCampaignGroupRepository =
            new LinkedInCampaignGroupRepository();

          const campaignGroup =
            await linkedInCampaignGroupRepository.getOneByAdSegmentId(
              adSegmentId,
            );

          if (!campaignGroup) {
            logger.info("No Campaign Group Found - Skipping budget update");
            return;
          }

          const campaignGroupBudget = campaignGroup.totalBudget;

          const audienceAbTestRepository = new AbTestRepository();

          const audienceAbTest = await audienceAbTestRepository.getOne(
            stage.id,
            "audience",
          );
          console.log("budget", campaignGroupBudget);
          if (!audienceAbTest) {
            console.log("[endStage.inngestFunction] No Audience AbTest Winner");
            return;
          }

          // update budget in linkedin
          const linkedInClient = await getLinkedInApiClientFromOrganizationId(
            orgAndAccountData.orgId,
          );
          if (!linkedInClient) {
            console.log(
              "[endStage.inngestFunction] No linkedin client instantiated for org",
              orgAndAccountData.orgId,
            );
            return;
          }
          const linkedInService = new LinkedInService(linkedInClient);

          if (audienceAbTest.status === "COMPLETED") {
            const winningAudienceId = audienceAbTest.winnerId;

            const linkedInCampaignRepository = new LinkedInCampaignRepository();

            const winningLinkedInCampaign =
              await linkedInCampaignRepository.getOneById(winningAudienceId);

            if (!winningLinkedInCampaign) {
              console.log(
                "[endStage.inngestFunction] Winning Linkedin Audience Not Found",
                adSegmentId,
              );
              return;
            }

            const winningCampaignUrn =
              winningLinkedInCampaign.linkedInCampaignUrn;

            // Determine budget
            const adProgram = orgAndAccountData.adProgram;
            console.log("Adprogram id", adProgram.id);
            const campaignType = adProgram.type;

            let dailyBudget = campaignGroupBudget;

            if (campaignType === "EVERGREEN") {
              dailyBudget = dailyBudget / 30;
            } else {
              const startDate = new Date(adProgram.startDatetime);

              const endDate = adProgram.endDatetime
                ? new Date(adProgram.endDatetime)
                : undefined;
              if (!endDate) {
                return;
              }

              const diff = Math.abs(startDate.getTime() - endDate.getTime());
              const diffDays = Math.ceil(diff / (1000 * 3600 * 24));

              dailyBudget = Math.min((dailyBudget / diffDays) * 2, dailyBudget);
            }

            // Update Budget in Linkedin
            const liServiceRes = linkedInService.updateCampaignDailyBudget({
              linkedInCampaignUrn: winningCampaignUrn,
              linkedInAdAccountUrn: orgAndAccountData.linkedInAdAccountUrn,
              dailyBudget: dailyBudget,
            });
            console.log("Daily Budget", dailyBudget);
            console.log("LI RES", liServiceRes);

            if (!liServiceRes) {
              console.log(
                "Error updating linkedin campaign budget for adsegmentid",
                adSegmentId,
              );
              return;
            }
          }
        }
      });
    }

    await step.run("startNextStage", async () => {
      await advertisingInngestClient.send({
        name: "linkedin/stage.run",
        data: {
          adSegmentId: stage.adSegmentId,
          organizationId: orgAndAccountData.orgId,
        },
      });
    });
  },
);
