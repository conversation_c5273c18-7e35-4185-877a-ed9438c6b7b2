import { NonRetriableError } from "inngest";
import { z } from "zod";

import { getSegmentUseCase } from "../../../core/application/useCases/getSegment.useCase";
import { SegmentService } from "../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../core/infrastructure/repositories/segment.repository";
import { LangfusePromptStorageService } from "../../../core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "../../../core/infrastructure/services/openAiCompletion.service";
import { OpenAiEmbeddingService } from "../../../core/infrastructure/services/openAiEmbedding.service";
import { PromptExecutionService } from "../../../core/infrastructure/services/promptExecutionService.service";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { UtilsLlmCompletionsService } from "../../../core/infrastructure/services/utilsLlmCompletions.service";
import { utilsLangfuseClient } from "../../../core/utils/langfuseClients";
import { contactService } from "../../../crm/domain/services/contact.service";
import { contactRepository } from "../../../crm/infrastructure/repositories/contact.repository";
import { MapKalosDataToLinkedInFacetEntitiesService } from "../../application/services/mapKalosDataToLinkedInFacetEntities.service";
import { AdAudienceService } from "../../domain/services/adAudience.service";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { SegmentAudienceTargetPrefabService } from "../../domain/services/segmentAudiencePrefab.service";
import { LinkedInAudienceTargetCriteria } from "../../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { SegmentAudiencePrefabRepository } from "../../infrastructure/repositories/segmentAudienceTargetPrefabRepository";
import { AdvertisingLlmCompletionsService } from "../../infrastructure/services/advertisingLlmCompletions.service";
import { VectorizedLinkedInFacetEntityStorageService } from "../../infrastructure/services/vectorizedLinkedInFacetEntityStorage.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";
import { advertisingLangfuseClient } from "../../utils/advertisingLangfuseClient";
import { generateLinkedInSkillsFromIcpDirect, generateLinkedInGroupsFromIcpDirect } from "../../../../../../services/advertising/src/functions/generateLinkedInTargetingFromIcp";

const eventBodySchema = z.object({
  adSegmentId: z.string(),
  organizationId: z.number(),
});
export const createAudienceInngestFunction =
  advertisingInngestClient.createFunction(
    { id: "create-audience" },
    { event: "linkedin/create-audience" },
    async ({ step, event }) => {
      const { adSegmentId, organizationId } = eventBodySchema.parse(event.data);
      const segment = await step.run("get-segment", async () => {
        try {

          const adSegmentRepository = new LinkedInAdSegmentRepository();

          const adSegment = await adSegmentRepository.getOne(adSegmentId);

          if (!adSegment) {
            throw new NonRetriableError("Ad segment not found");
          }
          const s = await getSegmentUseCase(
            {
              segmentId: adSegment.segmentId,
            },
            {
              organizationId: organizationId,
              segmentService: new SegmentService(segmentRepository),
              segmentRepository: segmentRepository,
            },
          );
          if (!s) {
            throw new NonRetriableError("Segment not found");
          }

          return s;
        } catch (e) {
          throw new NonRetriableError(
            `Segment validation failed: ${e as Error}`,
          );
        }
      });

      const verticalEntityStep = step.run("get-vertical-entity", async () => {
        if (segment.verticals.length === 0) {
          return null;
        }
        const kalosMapperService = mappingServiceFactory();
        const res: {
          facetEntities: {
            name: string;
            urn: string;
          }[];
        } = {
          facetEntities: [],
        };
        if (segment.verticals.length > 0) {
          for (const eachVertical of segment.verticals) {
            if (eachVertical.length == 0) {
              continue;
            }
            const currRes =
              await kalosMapperService.mapKalosDataToLinkedInFacetEntities({
                field: "Vertical",
                facetUrn: "urn:li:adTargetingFacet:industries",
                value: eachVertical,
              });
            res.facetEntities.push({
              name: currRes.name,
              urn: currRes.urn,
            });
          }
        }
        return res.facetEntities;
      });

      const numberOfEmployeesEntityStep = step.run(
        "get-number-of-employees-entity",
        async () => {
          const kalosMapperService = mappingServiceFactory();
          if (
            !segment.numberOfEmployeesLowBound &&
            !segment.numberOfEmployeesHighBound
          ) {
            return null;
          } else if (
            segment.numberOfEmployeesLowBound &&
            !segment.numberOfEmployeesHighBound
          ) {
            return await kalosMapperService.mapKalosDataToLinkedInFacetEntities(
              {
                field: "Number of Employees",
                facetUrn: "urn:li:adTargetingFacet:staffCountRanges",
                value: `>${segment.numberOfEmployeesLowBound}`,
              },
            );
          } else if (
            segment.numberOfEmployeesHighBound &&
            !segment.numberOfEmployeesLowBound
          ) {
            return await kalosMapperService.mapKalosDataToLinkedInFacetEntities(
              {
                field: "Number of Employees",
                facetUrn: "urn:li:adTargetingFacet:staffCountRanges",
                value: `<${segment.numberOfEmployeesHighBound}`,
              },
            );
          } else {
            return await kalosMapperService.mapKalosDataToLinkedInFacetEntities(
              {
                field: "Number of Employees",
                facetUrn: "urn:li:adTargetingFacet:staffCountRanges",
                value: `${segment.numberOfEmployeesLowBound} - ${segment.numberOfEmployeesHighBound}`,
              },
            );
          }
        },
      );

      const annualRevenueEntityStep = step.run(
        "get-annual-revenue-entity",
        async () => {
          const kalosMapperService = mappingServiceFactory();
          if (
            !segment.annualRevenueLowBound &&
            !segment.annualRevenueHighBound
          ) {
            return null;
          } else if (
            segment.annualRevenueLowBound &&
            !segment.annualRevenueHighBound
          ) {
            return await kalosMapperService.mapKalosDataToLinkedInFacetEntities(
              {
                field: "Annual Revenue",
                facetUrn: "urn:li:adTargetingFacet:revenue",
                value: `>${segment.annualRevenueHighBound}`,
              },
            );
          } else if (
            !segment.annualRevenueLowBound &&
            segment.annualRevenueHighBound
          ) {
            return await kalosMapperService.mapKalosDataToLinkedInFacetEntities(
              {
                field: "Annual Revenue",
                facetUrn: "urn:li:adTargetingFacet:revenue",
                value: `<${segment.annualRevenueHighBound}`,
              },
            );
          } else {
            return await kalosMapperService.mapKalosDataToLinkedInFacetEntities(
              {
                field: "Annual Revenue",
                facetUrn: "urn:li:adTargetingFacet:revenue",
                value: `${segment.annualRevenueLowBound} - ${segment.annualRevenueHighBound}`,
              },
            );
          }
        },
      );

      const jobFunctionEntityStep = step.run(
        "get-job-function-entity",
        async () => {
          if (!segment.jobFunction) {
            return null;
          }
          const kalosMapperService = mappingServiceFactory();
          const res =
            await kalosMapperService.mapKalosDataToLinkedInFacetEntities({
              field: "Job Function",
              facetUrn: "urn:li:adTargetingFacet:jobFunctions",
              value: segment.jobFunction,
            });
          return res;
        },
      );

      const jobTitlesEntityStep = step.run(
        "get-job-titles-entities",
        async () => {
          if (!segment.jobFunction) {
            return null;
          }
          const contacts = await contactService.getContactsWithJobFunction(
            segment.jobFunction,
            event.data.organizationId,
            {
              contactRepository: contactRepository,
            },
          );
          const titles = contacts
            .map((each) => each.title)
            .filter((each) => each !== null && each !== undefined);
          const kalosMapperService = mappingServiceFactory();
          const res =
            await kalosMapperService.mapKalosJobTitlesToLinkedInJobTitles({
              jobTitles: titles,
              jobFunction: segment.jobFunction,
              jobSeniority:
                segment.jobSeniority || "NOT GIVEN(INFER FROM JOB TITLES)",
            });
          return res;
        },
      );

      const skillsEntityStep = step.run(
        "generate-linkedin-skills",
        async () => {
          try {
            const eventData = {
              organizationId: organizationId,
              segmentId: adSegmentId,
              campaignId: `campaign-${adSegmentId}`,
              positioningKeyPoints: `Target audience in ${segment.verticals?.join(", ") || "various industries"} with ${segment.jobFunction || "professional"} roles`,
              organizationName: "Organization",
              productCategory: segment.verticals?.[0] || "B2B Software",
              adTargeting: {
                jobFunction: segment.jobFunction,
                jobSeniority: segment.jobSeniority,
                verticals: segment.verticals,
                numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
                numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
                annualRevenueLowBound: segment.annualRevenueLowBound,
                annualRevenueHighBound: segment.annualRevenueHighBound,
              },
            };

            const result = await generateLinkedInSkillsFromIcpDirect(eventData);

            if (result && result.validSkills && result.validSkills.length > 0) {
              return result.validSkills;
            } else {
              console.warn("⚠️ No valid LinkedIn skills found for segment");
              return null;
            }
          } catch (error) {
            console.error("❌ Error generating LinkedIn skills:", error);
            return null;
          }
        },
      );

      const senioritiesEntityStep = step.run(
        "generate-linkedin-seniorities",
        async () => {
          try {
            if (!segment.jobFunction) {
              return null;
            }

            const contacts = await contactService.getContactsWithJobFunction(
              segment.jobFunction,
              event.data.organizationId,
              {
                contactRepository: contactRepository,
              },
            );

            const titles = contacts
              .map((each) => each.title)
              .filter((each) => each !== null && each !== undefined);

            if (titles.length === 0) {
              console.warn("⚠️ No job titles found for seniority generation");
              return null;
            }

            const vectorizedService = new VectorizedLinkedInFacetEntityStorageService(
              new OpenAiEmbeddingService()
            );
            const linkedInSeniorityFacets = await vectorizedService.searchForFacetEntity({
              facetUrn: "urn:li:adTargetingFacet:seniorities",
              searchQuery: " ",
              numberOfResults: 99,
            });

            const facetsJson = JSON.stringify(linkedInSeniorityFacets.map((facet: any) => ({
              name: facet.faceEntityName,
              urn: facet.facetEntityUrn
            })));

            const advertisingLlmCompletionsService = new AdvertisingLlmCompletionsService(
              new PromptExecutionService(
                new LangfusePromptStorageService(advertisingLangfuseClient),
                new OpenAiCompletionService()
              )
            );

            const result = await advertisingLlmCompletionsService.generateLinkedInSenioritiesFromJobTitles({
              jobTitles: titles,
              facetEntities: facetsJson,
            });

            const urnMatches = result.match(/urn:li:seniority:\d+/g);

            if (!urnMatches || urnMatches.length === 0) {
              console.warn("⚠️ No valid seniority URNs found in response");
              return null;
            }

            const seniorities = urnMatches.map((urn: string) => {
              const facet = linkedInSeniorityFacets.find((f: any) => f.facetEntityUrn === urn);
              return facet ? { name: facet.faceEntityName, urn: facet.facetEntityUrn } : null;
            }).filter(Boolean);

            return seniorities;

          } catch (error) {
            console.error("❌ Error generating LinkedIn seniorities:", error);
            return null;
          }
        },
      );

      const [verticalEntities, numberOfEmployeesEntity, annualRevenueEntity,
        jobFunctionEntity, jobTitlesEntities, skillsEntities, senioritiesEntities] = await Promise.all([
          verticalEntityStep,
          numberOfEmployeesEntityStep,
          annualRevenueEntityStep,
          jobFunctionEntityStep,
          jobTitlesEntityStep,
          skillsEntityStep,
          senioritiesEntityStep,
        ]);

      const groupsEntityStep = step.run(
        "generate-linkedin-groups-with-context",
        async () => {

          const skillsForPrompt = skillsEntities?.map((skill: any) => skill.name || skill).join(", ") || "No skills generated";
          const jobTitlesForPrompt = jobTitlesEntities?.map((title: any) => title.name || title).join(", ") || "No job titles generated";

          try {
            const eventData = {
              organizationId: organizationId,
              segmentId: adSegmentId,
              campaignId: `campaign-${adSegmentId}`,
              positioningKeyPoints: `Target audience in ${segment.verticals?.join(", ") || "various industries"} with ${segment.jobFunction || "professional"} roles`,
              organizationName: "Organization",
              productCategory: segment.verticals?.[0] || "B2B Software",
              adTargeting: {
                jobFunction: segment.jobFunction,
                jobSeniority: segment.jobSeniority,
                verticals: segment.verticals,
                numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
                numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
                annualRevenueLowBound: segment.annualRevenueLowBound,
                annualRevenueHighBound: segment.annualRevenueHighBound,
              },
              generatedSkills: skillsForPrompt,
              generatedJobTitles: jobTitlesForPrompt,
            };

            const result = await generateLinkedInGroupsFromIcpDirect(eventData);

            if (result && result.validGroups && result.validGroups.length > 0) {
              return result.validGroups;
            } else {
              console.warn("⚠️ No valid LinkedIn groups found for segment");
              return null;
            }
          } catch (error) {
            console.error("❌ Error generating LinkedIn groups:", error);
            return null;
          }
        },
      );

      const groupsEntities = await groupsEntityStep;

      const jobFunctionExclusionsEntityStep = step.run(
        "get-job-function-exclusions-entity",
        async () => {
          if (!jobFunctionEntity) {
            return null;
          }
          const kalosMapperService = mappingServiceFactory();
          const res = await kalosMapperService.getJobFunctionExlusions([
            jobFunctionEntity.urn,
          ]);
          return res;
        },
      );

      const staffCountExclusionsEntityStep = step.run(
        "get-staff-count-exclusions-entity",
        async () => {
          if (!numberOfEmployeesEntity) {
            return null;
          }
          const kalosMapperService = mappingServiceFactory();
          const res = await kalosMapperService.getStaffCountExclusions([
            numberOfEmployeesEntity.urn,
          ]);
          return res;
        },
      );

      const [jobFunctionExclusionsEntity, staffCountExclusionsEntity] =
        await Promise.all([
          jobFunctionExclusionsEntityStep,
          staffCountExclusionsEntityStep,
        ]);

      const x = await step.run("save", async () => {
        const adAudienceRepository = new LinkedInAdAudienceRepository();
        const adAudienceService = new AdAudienceService(adAudienceRepository);

        type BaseOr = {
          facetUrn: string;
          facetName: string;
          facetEntites: {
            facetUrn: string;
            entityUrn: string;
            entityName: string;
          }[];
        };

        const verticals: BaseOr | undefined =
          !verticalEntities || verticalEntities.length === 0
            ? undefined
            : {
              facetUrn: "urn:li:adTargetingFacet:industries",
              facetName: "Industry",
              facetEntites: verticalEntities.map((vertical) => ({
                facetUrn: "urn:li:adTargetingFacet:industries",
                entityUrn: vertical.urn,
                entityName: vertical.name,
              })),
            };

        const numberOfEmployees: BaseOr | undefined = !numberOfEmployeesEntity
          ? undefined
          : {
            facetUrn: "urn:li:adTargetingFacet:staffCountRanges",
            facetName: "Number of Employees",
            facetEntites: [
              {
                facetUrn: "urn:li:adTargetingFacet:staffCountRanges",
                entityUrn: numberOfEmployeesEntity.urn,
                entityName: numberOfEmployeesEntity.name,
              },
            ],
          };

        const annualRevenue: BaseOr | undefined = !annualRevenueEntity
          ? undefined
          : {
            facetUrn: "urn:li:adTargetingFacet:revenue",
            facetName: "Annual Revenue",
            facetEntites: [
              {
                facetUrn: "urn:li:adTargetingFacet:revenue",
                entityUrn: annualRevenueEntity.urn,
                entityName: annualRevenueEntity.name,
              },
            ],
          };

        const jobTitles: BaseOr | undefined =
          !jobTitlesEntities || jobTitlesEntities.length === 0
            ? undefined
            : {
              facetUrn: "urn:li:adTargetingFacet:titles",
              facetName: "Titles",
              facetEntites: jobTitlesEntities.map((title) => ({
                facetUrn: "urn:li:adTargetingFacet:titles",
                entityUrn: title.urn,
                entityName: title.name,
              })),
            };

        const jobFunctions: BaseOr | undefined = !jobFunctionEntity
          ? undefined
          : {
            facetUrn: "urn:li:adTargetingFacet:jobFunctions",
            facetName: "Job Function",
            facetEntites: [
              {
                facetUrn: "urn:li:adTargetingFacet:jobFunctions",
                entityUrn: jobFunctionEntity.urn,
                entityName: jobFunctionEntity.name,
              },
            ],
          };

        const seniorities: BaseOr | undefined =
          !senioritiesEntities || senioritiesEntities.length === 0
            ? undefined
            : {
              facetUrn: "urn:li:adTargetingFacet:seniorities",
              facetName: "Seniorities",
              facetEntites: senioritiesEntities.map((seniority: any) => ({
                facetUrn: "urn:li:adTargetingFacet:seniorities",
                entityUrn: seniority.urn,
                entityName: seniority.name,
              })),
            };

        const skills: BaseOr | undefined =
          !skillsEntities || skillsEntities.length === 0
            ? undefined
            : {
              facetUrn: "urn:li:adTargetingFacet:skills",
              facetName: "Skills",
              facetEntites: skillsEntities.map((skill: any) => ({
                facetUrn: "urn:li:adTargetingFacet:skills",
                entityUrn: skill.urn,
                entityName: skill.name,
              })),
            };

        const groups: BaseOr | undefined =
          !groupsEntities || groupsEntities.length === 0
            ? undefined
            : {
              facetUrn: "urn:li:adTargetingFacet:groups",
              facetName: "Groups",
              facetEntites: groupsEntities.map((group: any) => ({
                facetUrn: "urn:li:adTargetingFacet:groups",
                entityUrn: group.urn,
                entityName: group.name,
              })),
            };

        const jobTitlesTargetting: LinkedInAudienceTargetCriteria = {
          include: {
            and: [],
          },
        };

        const jobFunctionsTargetting: LinkedInAudienceTargetCriteria = {
          include: {
            and: [],
          },
        };

        const skillsTargetting: LinkedInAudienceTargetCriteria = {
          include: {
            and: [],
          },
        };

        const groupsTargetting: LinkedInAudienceTargetCriteria = {
          include: {
            and: [],
          },
        };

        if (jobTitles) {
          jobTitlesTargetting.include.and.push({
            or: [jobTitles],
          });
        }

        if (jobFunctions) {
          jobFunctionsTargetting.include.and.push({
            or: [jobFunctions],
          });
        }

        if (skills) {
          skillsTargetting.include.and.push({
            or: [skills],
          });
        }

        if (groups) {
          groupsTargetting.include.and.push({
            or: [groups],
          });
        }

        if (verticals) {
          jobFunctionsTargetting.include.and.push({
            or: [verticals],
          });
          jobTitlesTargetting.include.and.push({
            or: [verticals],
          });
          skillsTargetting.include.and.push({
            or: [verticals],
          });
          groupsTargetting.include.and.push({
            or: [verticals],
          });
        }

        if (numberOfEmployees) {
          jobFunctionsTargetting.include.and.push({
            or: [numberOfEmployees],
          });
          jobTitlesTargetting.include.and.push({
            or: [numberOfEmployees],
          });
          skillsTargetting.include.and.push({
            or: [numberOfEmployees],
          });
          groupsTargetting.include.and.push({
            or: [numberOfEmployees],
          });
        }
        if (annualRevenue) {
          jobFunctionsTargetting.include.and.push({
            or: [annualRevenue],
          });
          jobTitlesTargetting.include.and.push({
            or: [annualRevenue],
          });
          skillsTargetting.include.and.push({
            or: [annualRevenue],
          });
          groupsTargetting.include.and.push({
            or: [annualRevenue],
          });
        }

        const usaLocation: BaseOr = {
          facetUrn: "urn:li:adTargetingFacet:locations",
          facetName: "Locations",
          facetEntites: [
            {
              facetUrn: "urn:li:adTargetingFacet:locations",
              entityUrn: "urn:li:geo:103644278",
              entityName: "United States"
            }
          ]
        };

        jobFunctionsTargetting.include.and.push({
          or: [usaLocation],
        });
        jobTitlesTargetting.include.and.push({
          or: [usaLocation],
        });
        skillsTargetting.include.and.push({
          or: [usaLocation],
        });
        groupsTargetting.include.and.push({
          or: [usaLocation],
        });

        if (seniorities) {
          if (skills && skillsTargetting.include.and.length > 0) {
            skillsTargetting.include.and.push({
              or: [seniorities],
            });
          }
          if (groups && groupsTargetting.include.and.length > 0) {
            groupsTargetting.include.and.push({
              or: [seniorities],
            });
          }
        }

        let functionsExclusionsOr: BaseOr | undefined = undefined;
        let staffCountExclusionsOr: BaseOr | undefined = undefined;

        if (jobFunctionExclusionsEntity) {
          const facetUrn = jobFunctionExclusionsEntity[0]?.facetUrn;
          if (facetUrn) {
            functionsExclusionsOr = {
              facetUrn: facetUrn,
              facetName: "Job Function",
              facetEntites: jobFunctionExclusionsEntity.map((each) => ({
                facetUrn: each.facetUrn,
                entityUrn: each.facetEntityUrn,
                entityName: each.faceEntityName,
              })),
            };
            jobFunctionsTargetting.exclude = {
              or: [functionsExclusionsOr],
            };
          }
        }

        if (staffCountExclusionsEntity) {
          const facetUrn = staffCountExclusionsEntity[0]?.facetUrn;
          if (facetUrn) {
            staffCountExclusionsOr = {
              facetUrn: facetUrn,
              facetName: "Number of Employees",
              facetEntites: staffCountExclusionsEntity.map((each) => ({
                facetUrn: each.facetUrn,
                entityUrn: each.facetEntityUrn,
                entityName: each.faceEntityName,
              })),
            };
            if (jobFunctionsTargetting.exclude) {
              jobFunctionsTargetting.exclude.or.push(staffCountExclusionsOr);
            } else {
              jobFunctionsTargetting.exclude = {
                or: [staffCountExclusionsOr],
              };
            }
            if (jobTitlesTargetting.exclude) {
              jobTitlesTargetting.exclude.or.push(staffCountExclusionsOr);
            } else {
              jobTitlesTargetting.exclude = {
                or: [staffCountExclusionsOr],
              };
            }
          }
        }

        const transactionManagerService = new TransactionManagerService();
        const segmentAudienceTargetPrefabRepository =
          new SegmentAudiencePrefabRepository();
        const segmentAudiencePrefabService =
          new SegmentAudienceTargetPrefabService(
            segmentAudienceTargetPrefabRepository,
          );

        await transactionManagerService.startTransaction(async (tx) => {
          await adAudienceService.createOne(
            {
              linkedInAdSegmentId: adSegmentId,
              toBeUsed: true,
              audiencePopulated: false,
              audienceTargetCriteria: jobFunctionsTargetting,
            },
            tx,
          );

          await adAudienceService.createOne(
            {
              linkedInAdSegmentId: adSegmentId,
              toBeUsed: true,
              audiencePopulated: false,
              audienceTargetCriteria: jobTitlesTargetting,
            },
            tx,
          );

          if (skills && skillsTargetting.include.and.length > 0) {
            await adAudienceService.createOne(
              {
                linkedInAdSegmentId: adSegmentId,
                toBeUsed: true,
                audiencePopulated: false,
                audienceTargetCriteria: skillsTargetting,
              },
              tx,
            );
          }

          if (groups && groupsTargetting.include.and.length > 0) {
            await adAudienceService.createOne(
              {
                linkedInAdSegmentId: adSegmentId,
                toBeUsed: true,
                audiencePopulated: false,
                audienceTargetCriteria: groupsTargetting,
              },
              tx,
            );
          }

          const audiencesToSave = [];

          audiencesToSave.push(jobFunctionsTargetting);
          audiencesToSave.push(jobTitlesTargetting);
          if (skills && skillsTargetting.include.and.length > 0) {
            audiencesToSave.push(skillsTargetting);
          }
          if (groups && groupsTargetting.include.and.length > 0) {
            audiencesToSave.push(groupsTargetting);
          }

          await segmentAudiencePrefabService.setForSegment(
            {
              segmentId: segment.id,
              audiences: audiencesToSave,
            },
            tx,
          );

          const adSegmentRepository = new LinkedInAdSegmentRepository();
          const adSegmentService = new AdSegmentService(adSegmentRepository);
          await adSegmentService.setReady(adSegmentId, true, tx);
        });
      });
    },
  );

function mappingServiceFactory() {
  const embeddingService = new OpenAiEmbeddingService();

  const vectorizedLinkedInFacetEntityStorageService =
    new VectorizedLinkedInFacetEntityStorageService(embeddingService);

  const llmCompletionService = new OpenAiCompletionService();
  const langfusePromptStorageService = new LangfusePromptStorageService(
    advertisingLangfuseClient,
  );
  const promptExecutionService = new PromptExecutionService(
    langfusePromptStorageService,
    llmCompletionService,
  );

  const utilsLangfusePromptStorageService = new LangfusePromptStorageService(
    utilsLangfuseClient,
  );

  const utilsExecutionService = new PromptExecutionService(
    utilsLangfusePromptStorageService,
    llmCompletionService,
  );

  const advertisingLlmCompletionsService = new AdvertisingLlmCompletionsService(
    promptExecutionService,
  );
  const utilsLlmCompletionsService = new UtilsLlmCompletionsService(
    utilsExecutionService,
  );

  const kalosMapperService = new MapKalosDataToLinkedInFacetEntitiesService(
    vectorizedLinkedInFacetEntityStorageService,
    advertisingLlmCompletionsService,
    utilsLlmCompletionsService,
  );
  return kalosMapperService;
}
