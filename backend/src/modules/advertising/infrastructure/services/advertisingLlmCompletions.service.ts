import { ILLMCompletionService } from "../../../core/application/interfaces/infrastructure/services/llmCompletionService.interface";
import { IPromptExecutionService } from "../../../core/application/interfaces/infrastructure/services/promptExecution.service.interface";
import { IPromptStorageService } from "../../../core/application/interfaces/infrastructure/services/promptStorage.service.interface";
import { IAdvertisingLlmCompletionsService } from "../../application/interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { ConversationSubjectCopyType } from "../../domain/valueObjects/conversationSubjectCopyType";

export class AdvertisingLlmCompletionsService
  implements IAdvertisingLlmCompletionsService {
  constructor(
    private readonly promptExecutionService: IPromptExecutionService,
  ) { }

  async generateAdSegmentConversationBaseCopy(input: {
    positioningKeyPoints: string;
    organizationName: string;
    useCases: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "generate-conversation-base-copy",
      {
        company: input.organizationName,
        ad_site: "LinkedIn",
        product_positioning: input.positioningKeyPoints,
        target_audience: targetAudience,
        case_studies: input.useCases,
      },
      {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    );
    return res;
  }
  async *generateSocialPostHeadline(
    input: {
      baseCopy: string;
      adSegmentValueProp: string;
      styleGuide: string;
      exampleSocialPost: string[];
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "create-ad-title",
      {
        company: "Not provided",
        ad_site: "LinkedIn",
        product_positioning: input.adSegmentValueProp,
        style_guide: input.styleGuide,
        example_ads: input.exampleSocialPost.join("\n-------\n"),
        target_audience: targetAudience,
        topic: input.adSegmentValueProp,
      },
    );
    const res = await ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *getnerateConversationTitle(
    input: {
      company: string;
      adSegmentValueProp: string;
      subjectType: ConversationSubjectCopyType;
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "conversation-ad-subject",
      {
        company: "Not provided",
        ad_site: "LinkedIn",
        target_audience: targetAudience,
        topic: input.adSegmentValueProp,
        subject_type: input.subjectType,
      },
    );
    const res = await ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateSocialPostBody(
    input: {
      baseCopy: string;
      adSegmentValueProp: string;
      styleGuide: string;
      exampleSocialPost: string[];
      positioning: string;
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "create-base-ad",
      {
        company: "Not provided",
        ad_site: "LinkedIn",
        product_positioning: input.positioning,
        style_guide: input.styleGuide,
        example_ads: input.exampleSocialPost.join("\n-------\n"),
        target_audience: targetAudience,
      },
    );
    compiledPrompt.push({
      role: "assistant",
      content: input.baseCopy,
    });
    compiledPrompt.push({
      role: "user",
      content: `Rewrite the exact same social post but change the first sentence to be aligned to the following topic: ${input.adSegmentValueProp}\n Make sure to keep the same tone and style as the original post's first sentance. Keep the structure and punctuation the same of the first sentance.`,
    });
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateConversationBody(
    input: {
      baseCopy: string;
      adSegmentValueProp: string;
      positioning: string;
      subject: string;
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "create-conversation-body",
      {
        company: "Not provided",
        ad_site: "LinkedIn",
        product_positioning: input.positioning,
        target_audience: targetAudience,
      },
    );
    compiledPrompt.push({
      role: "assistant",
      content: input.baseCopy,
    });
    compiledPrompt.push({
      role: "user",
      content: `Rewrite the exact same email but change the beginning to be aligned to the following topic: ${input.adSegmentValueProp}, and update the conversation and to be aligned to the following subject line: "${input.subject}".\n\nIn the original, there is an offer to provide a strategy session near the end. If the new subject is a hard or soft offer, then remove the strategy session and reiterate the offer in the subject.\n\nIf the subject does not contain an offer, then leave the strategy session as is. \n\nMake sure to keep the same tone and style as the original post's first sentence and last sentence. Keep the structure and punctuation the same of the first sentence.`,
    });
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async mapKalosJobTitlesToLinkedInJobTitles(
    jobTitles: string[],
    jobFunction: string,
    jobSeniority: string,
    facetEntites: string,
  ): Promise<string> {
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "map-job-function-seniority-and-titles-to-linkedIn-job-title-facet-entities",
      {
        crm_job_titles: jobTitles.join("\n"),
        function: jobFunction,
        seniority: jobSeniority,
        facets: facetEntites,
      },
      {
        model: "gpt-4o",
        temperature: 0,
      },
    );
    return res;
  }

  async mapKalosFieldToLinkedInFacetEntity(
    field: string,
    facetEntities: string,
    fieldValue: string,
  ): Promise<string> {
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "map-kalos-field-value-linkedIn-facet-entity",
      {
        field: field,
        facets: facetEntities,
        value: fieldValue,
      },
      {
        model: "gpt-4o",
        temperature: 0,
      },
    );
    return res;
  }

  async generateAudienceKeyPointsFromPositioning(input: {
    positioning: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "positioning-key-points",
      {
        positioning: input.positioning,
        audience: targetAudience,
      },
      {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    );
    return res;
  }

  async generateUseCasesFromCaseStudies(input: {
    caseStudies: string[];
    targetAudience: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string> {
    const targetAudience = `Industry: ${input.targetAudience.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.targetAudience.jobFunction ?? "N/A"} \n Job Seniority: ${input.targetAudience.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.targetAudience.annualRevenueLowBound} - ${input.targetAudience.annualRevenueHighBound} \n Number of Employees: ${input.targetAudience.numberOfEmployeesLowBound} - ${input.targetAudience.numberOfEmployeesHighBound}`;
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "get-use-cases",
      {
        case_studies: input.caseStudies.join("\n-------\n"),
        audience: targetAudience,
      },
      {
        model: "gpt-4o",
        temperature: 0,
        topP: 1,
      },
    );
    return res;
  }

  async generateAdSegmentSocialPostBaseCopy(input: {
    positioningKeyPoints: string;
    exampleSocialPost: string[];
    organizationName: string;
    useCases: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "generate-base-copy-with-example-ads",
      {
        company: input.organizationName,
        ad_site: "LinkedIn",
        product_positioning: input.positioningKeyPoints,
        example_ads: input.exampleSocialPost.join("\n-------\n"),
        target_audience: targetAudience,
        case_studies: input.useCases,
      },
      {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    );
    return res;
  }

  async modifyAdCopyToFitSocialPostStyleGuide(input: {
    adCopy: string;
    positioningKeyPoints: string;
    exampleSocialPost: string[];
    organizationName: string;
    useCases: string;
    styleGuide: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;
    const res = await this.promptExecutionService.executePromptTextCompletion(
      "style-guide",
      {
        company: input.organizationName,
        ad_site: "LinkedIn",
        product_positioning: input.positioningKeyPoints,
        style_guide: input.styleGuide,
        example_ads: input.exampleSocialPost.join("\n-------\n"),
        target_audience: targetAudience,
        case_studies: input.useCases,
        ad: input.adCopy,
      },
      {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    );
    return res;
  }

  async *generateRefinedAdSegmentSocialPostBaseCopy(
    input: {
      baseCopy: string;
      feedback: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "rewrite-post-agent",
      {
        social_post: input.baseCopy,
        feedback: input.feedback,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateRefinedSocialPostTitle(
    input: {
      baseCopy: string;
      feedback: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "rewrite-social-post-title",
      {
        social_post: input.baseCopy,
        feedback: input.feedback,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }
  async *generateRefinedConversationSubject(
    input: { baseCopy: string; feedback: string },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ) {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "subject-rewrite",
      {
        subject: input.baseCopy,
        user_feedback: input.feedback,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }
  async *generateRefinedConversationMessage(
    input: { baseCopy: string; feedback: string },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ) {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "convo-rewrite",
      {
        message: input.baseCopy,
        feedback: input.feedback,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateConversationMessageCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "sponsored-inmail-message",
      {
        inmail_message: input.standardCopy,
        format: input.type,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateSocialPostBodyCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "sponsored-content-body",
      {
        inmail_message: input.standardCopy,
        format: input.type,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateSocialPostHeadlineCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "sponsored-content-cta",
      {
        inmail_message: input.standardCopy,
        format: input.type,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async *generateConversationCallToActionCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string> {
    const compiledPrompt = await ctx.promptStorage.getCompiledPrompt(
      "sponsored-inmail-cta",
      {
        inmail_message: input.standardCopy,
        format: input.type,
      },
    );
    const res = ctx.lllmCompletions.getStreamingTextCompletion({
      messages: compiledPrompt,
      config: {
        model: "gpt-4o",
        temperature: 0.7,
        topP: 0.2,
      },
    });
    for await (const each of res) {
      yield each;
    }
  }

  async generateLinkedInSkillsFromICP(input: {
    positioningKeyPoints: string;
    organizationName: string;
    productCategory: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;

    const res = await this.promptExecutionService.executePromptTextCompletion(
      "generate-linkedin-skills-from-icp",
      {
        company: input.organizationName,
        product_positioning: input.positioningKeyPoints,
        product_category: input.productCategory,
        target_audience: targetAudience,
        job_function: input.adTargeting.jobFunction ?? "N/A",
        job_seniority: input.adTargeting.jobSeniority ?? "N/A",
        industries: input.adTargeting.verticals?.join(", ") ?? "N/A",
      },
      {
        model: "gpt-4o",
        temperature: 0,
      },
    );

    return res;
  }

  async generateLinkedInGroupsFromICP(input: {
    positioningKeyPoints: string;
    organizationName: string;
    productCategory: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
    generatedSkills?: string;
    generatedJobTitles?: string;
  }): Promise<string> {
    const targetAudience = `Industry: ${input.adTargeting.verticals?.join(", ") ?? "N/A"} \n Job Function: ${input.adTargeting.jobFunction ?? "N/A"} \n Job Seniority: ${input.adTargeting.jobSeniority ?? "N/A"} \n Annual Revenue: ${input.adTargeting.annualRevenueLowBound} - ${input.adTargeting.annualRevenueHighBound} \n Number of Employees: ${input.adTargeting.numberOfEmployeesLowBound} - ${input.adTargeting.numberOfEmployeesHighBound}`;

    const res = await this.promptExecutionService.executePromptTextCompletion(
      "generate-linkedin-groups-from-icp",
      {
        company: input.organizationName,
        product_positioning: input.positioningKeyPoints,
        product_category: input.productCategory,
        target_audience: targetAudience,
        job_function: input.adTargeting.jobFunction ?? "N/A",
        job_seniority: input.adTargeting.jobSeniority ?? "N/A",
        industries: input.adTargeting.verticals?.join(", ") ?? "N/A",
        generated_skills: input.generatedSkills ?? "No skills generated",
        generated_job_titles: input.generatedJobTitles ?? "No job titles generated",
      },
      {
        model: "gpt-4o",
        temperature: 0,
      },
    );

    return res;
  }

  async generateLinkedInSenioritiesFromJobTitles(input: {
    jobTitles: string[];
    facetEntities: string;
  }): Promise<string> {

    const res = await this.promptExecutionService.executePromptTextCompletion(
      "generate-linkedin-seniority",
      {
        crm_job_titles: JSON.stringify(input.jobTitles),
        facets: input.facetEntities,
      },
      {
        model: "gpt-4o-mini",
      },
    );

    return res;
  }
}
