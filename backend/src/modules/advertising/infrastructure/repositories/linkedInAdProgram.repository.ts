import { and, eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInAdAccountTable } from "../../../../database/schemas/advertising/linkedInAdAccount.table";
import { linkedInAdFormatTable } from "../../../../database/schemas/advertising/linkedInAdFormat.table";
import { linkedInAdProgramTable } from "../../../../database/schemas/advertising/linkedInAdProgram.table";
import { linkedInAdSegmentTable } from "../../../../database/schemas/advertising/linkedInAdSegment.table";
import { LinkedInAdProgramRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { AdProgram } from "../../domain/entites/adProgram";
import { LinkedInAdFormat } from "../../domain/valueObjects/linkedinAdFormat";

export class LinkedInAdProgramRepository
  implements LinkedInAdProgramRepositoryInterface
{
  async createOne(input: AdProgram, tx?: Transaction) {
    const invoker = tx ?? db;
    const adFormats = await invoker.select().from(linkedInAdFormatTable);
    const adFormat = adFormats.find(
      (format) =>
        format.type === input.adFormat.type &&
        format.format === input.adFormat.format,
    );
    if (!adFormat) {
      throw new Error("Ad format not found in database");
    }
    await invoker.insert(linkedInAdProgramTable).values({
      id: input.id,
      linkedInAdAccountId: input.linkedInAdAccountId,
      title: input.title,
      objectiveType: input.objectiveType,
      startDatetime: input.startDatetime,
      endDatetime: input.endDatetime,
      destinationUrl: input.destinationUrl,
      totalBudget: input.totalBudget,
      monthlyBudget: input.monthlyBudget,
      adFormatId: adFormat.id,
      leadGenFormUrn:
        input.objectiveType === "LEAD_GENERATION" ? input.leadGenForm : null,
      type: input.type,
    });
    return input;
  }

  async getOne(id: string) {
    const dbRes = await db
      .select()
      .from(linkedInAdProgramTable)
      .where(eq(linkedInAdProgramTable.id, id))
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      )
      .limit(1);
    if (!dbRes[0]) {
      return null;
    }

    const adProgram = AdProgram({
      id: dbRes[0].linked_in_ad_program.id,
      linkedInAdAccountId: dbRes[0].linked_in_ad_program.linkedInAdAccountId,
      title: dbRes[0].linked_in_ad_program.title,
      objectiveType: dbRes[0].linked_in_ad_program.objectiveType,
      startDatetime: dbRes[0].linked_in_ad_program.startDatetime,
      endDatetime: dbRes[0].linked_in_ad_program.endDatetime,
      totalBudget: dbRes[0].linked_in_ad_program.totalBudget,
      monthlyBudget: dbRes[0].linked_in_ad_program.monthlyBudget,
      leadGenForm: dbRes[0].linked_in_ad_program.leadGenFormUrn ?? null,
      destinationUrl: dbRes[0].linked_in_ad_program.destinationUrl ?? null,
      adFormat: {
        type: dbRes[0].linked_in_ad_format.type as
          | "SPONSORED_CONTENT"
          | "SPONSORED_CONVERSATION"
          | "SPONSORED_INMAIL",
        format: dbRes[0].linked_in_ad_format.format as "SINGLE_IMAGE",
      } as LinkedInAdFormat,
      dateCreated: dbRes[0].linked_in_ad_program.dateCreated,
      status: dbRes[0].linked_in_ad_program.status ?? "ARCHIVED_DRAFT",
      type: dbRes[0].linked_in_ad_program.type ?? "EVENT_DRIVEN",
    });
    return adProgram;
  }

  async getByIds(ids: string[], tx?: Transaction): Promise<AdProgram[]> {
    if (ids.length === 0) {
      return [];
    }

    const invoker = tx ?? db;
    const dbRes = await invoker
      .select()
      .from(linkedInAdProgramTable)
      .where(inArray(linkedInAdProgramTable.id, ids))
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      );

    return dbRes.map((each) => {
      return AdProgram({
        id: each.linked_in_ad_program.id,
        linkedInAdAccountId: each.linked_in_ad_program.linkedInAdAccountId,
        title: each.linked_in_ad_program.title,
        objectiveType: each.linked_in_ad_program.objectiveType,
        startDatetime: each.linked_in_ad_program.startDatetime,
        endDatetime: each.linked_in_ad_program.endDatetime,
        totalBudget: each.linked_in_ad_program.totalBudget,
        monthlyBudget: each.linked_in_ad_program.monthlyBudget,
        leadGenForm: each.linked_in_ad_program.leadGenFormUrn ?? null,
        destinationUrl: each.linked_in_ad_program.destinationUrl ?? null,
        adFormat: {
          type: each.linked_in_ad_format.type as
            | "SPONSORED_CONTENT"
            | "SPONSORED_CONVERSATION"
            | "SPONSORED_INMAIL",
          format: each.linked_in_ad_format.format as "SINGLE_IMAGE",
        } as LinkedInAdFormat,
        dateCreated: each.linked_in_ad_program.dateCreated,
        status: each.linked_in_ad_program.status ?? "ARCHIVED_DRAFT",
        type: each.linked_in_ad_program.type ?? "EVENT_DRIVEN",
      });
    });
  }

  async updateOne(input: AdProgram, tx?: Transaction) {
    const invoker = tx ?? db;
    const adFormats = await invoker.select().from(linkedInAdFormatTable);
    const adFormat = adFormats.find(
      (format) =>
        format.type === input.adFormat.type &&
        format.format === input.adFormat.format,
    );
    if (!adFormat) {
      throw new Error("Ad format not found in database");
    }

    await invoker
      .update(linkedInAdProgramTable)
      .set({
        linkedInAdAccountId: input.linkedInAdAccountId,
        title: input.title,
        objectiveType: input.objectiveType,
        startDatetime: input.startDatetime,
        endDatetime: input.endDatetime ?? null,
        totalBudget: input.totalBudget ?? null,
        monthlyBudget: input.monthlyBudget ?? null,
        adFormatId: adFormat.id,
        leadGenFormUrn:
          input.objectiveType === "LEAD_GENERATION" ? input.leadGenForm : null,
        destinationUrl:
          input.objectiveType === "LEAD_GENERATION"
            ? input.destinationUrl
            : null,
        type: input.type,
        status: input.status,
      })
      .where(eq(linkedInAdProgramTable.id, input.id));
    return input;
  }

  async checkIfAdProgramBelongsToOrganization(
    adProgramId: string,
    organizationId: number,
    tx?: Transaction,
  ): Promise<boolean> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInAdProgramTable)
      .where(
        and(
          eq(linkedInAdProgramTable.id, adProgramId),
          eq(linkedInAdAccountTable.organizationId, organizationId),
        ),
      )
      .innerJoin(
        linkedInAdAccountTable,
        eq(
          linkedInAdAccountTable.id,
          linkedInAdProgramTable.linkedInAdAccountId,
        ),
      );
    return res.length > 0;
  }

  async getForOrganization(organizationId: number) {
    const res = await db
      .select()
      .from(linkedInAdProgramTable)
      .innerJoin(
        linkedInAdAccountTable,
        eq(
          linkedInAdAccountTable.id,
          linkedInAdProgramTable.linkedInAdAccountId,
        ),
      )
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      )
      .where(eq(linkedInAdAccountTable.organizationId, organizationId));
    return res.map((each) => {
      return AdProgram({
        id: each.linked_in_ad_program.id,
        linkedInAdAccountId: each.linked_in_ad_program.linkedInAdAccountId,
        title: each.linked_in_ad_program.title,
        objectiveType: each.linked_in_ad_program.objectiveType,
        startDatetime: each.linked_in_ad_program.startDatetime,
        endDatetime: each.linked_in_ad_program.endDatetime,
        totalBudget: each.linked_in_ad_program.totalBudget,
        monthlyBudget: each.linked_in_ad_program.monthlyBudget,
        leadGenForm: each.linked_in_ad_program.leadGenFormUrn ?? null,
        destinationUrl: each.linked_in_ad_program.destinationUrl ?? null,
        adFormat: {
          type: each.linked_in_ad_format.type as
            | "SPONSORED_CONTENT"
            | "SPONSORED_CONVERSATION"
            | "SPONSORED_INMAIL",
          format: each.linked_in_ad_format.format as "SINGLE_IMAGE",
        } as LinkedInAdFormat,
        dateCreated: each.linked_in_ad_program.dateCreated,
        status: each.linked_in_ad_program.status ?? "ARCHIVED_DRAFT",
        type: each.linked_in_ad_program.type ?? "EVENT_DRIVEN",
      });
    });
  }

  async getFilteredCampaigns(
    organizationId: number,
    status?: string,
    adAccountId?: string,
  ) {
    // Build conditions for the query
    let conditions = [
      eq(linkedInAdAccountTable.organizationId, organizationId),
    ];

    // Add status filter if provided
    if (status) {
      const validStatuses = [
        "DRAFT",
        "ACTIVE",
        "ARCHIVED_DRAFT",
        "ARCHIVED_ACTIVE",
        "COMPLETED",
      ];
      if (validStatuses.includes(status)) {
        conditions.push(
          eq(
            linkedInAdProgramTable.status,
            status as
              | "DRAFT"
              | "ACTIVE"
              | "ARCHIVED_DRAFT"
              | "ARCHIVED_ACTIVE"
              | "COMPLETED",
          ),
        );
      }
    }

    // Add ad account filter if provided
    if (adAccountId) {
      conditions.push(
        eq(linkedInAdProgramTable.linkedInAdAccountId, adAccountId),
      );
    }

    const res = await db
      .select()
      .from(linkedInAdProgramTable)
      .innerJoin(
        linkedInAdAccountTable,
        eq(
          linkedInAdAccountTable.id,
          linkedInAdProgramTable.linkedInAdAccountId,
        ),
      )
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      )
      .where(and(...conditions));

    return res.map((each) => {
      return AdProgram({
        id: each.linked_in_ad_program.id,
        linkedInAdAccountId: each.linked_in_ad_program.linkedInAdAccountId,
        title: each.linked_in_ad_program.title,
        objectiveType: each.linked_in_ad_program.objectiveType,
        startDatetime: each.linked_in_ad_program.startDatetime,
        endDatetime: each.linked_in_ad_program.endDatetime,
        totalBudget: each.linked_in_ad_program.totalBudget,
        monthlyBudget: each.linked_in_ad_program.monthlyBudget,
        leadGenForm: each.linked_in_ad_program.leadGenFormUrn ?? null,
        destinationUrl: each.linked_in_ad_program.destinationUrl ?? null,
        adFormat: {
          type: each.linked_in_ad_format.type as
            | "SPONSORED_CONTENT"
            | "SPONSORED_CONVERSATION"
            | "SPONSORED_INMAIL",
          format: each.linked_in_ad_format.format as "SINGLE_IMAGE",
        } as LinkedInAdFormat,
        dateCreated: each.linked_in_ad_program.dateCreated,
        status: each.linked_in_ad_program.status ?? "ARCHIVED_DRAFT",
        type: each.linked_in_ad_program.type ?? "EVENT_DRIVEN",
      });
    });
  }

  async getByAdSegmentId(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<AdProgram | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInAdProgramTable)
      .innerJoin(
        linkedInAdSegmentTable,
        eq(linkedInAdSegmentTable.linkedInAdProgramId, linkedInAdProgramTable.id),
      )
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      )
      .where(eq(linkedInAdSegmentTable.id, adSegmentId));

    if (!res[0]) {
      return null;
    }

    const program = res[0];
    return AdProgram({
      id: program.linked_in_ad_program.id,
      linkedInAdAccountId: program.linked_in_ad_program.linkedInAdAccountId,
      title: program.linked_in_ad_program.title,
      objectiveType: program.linked_in_ad_program.objectiveType,
      startDatetime: program.linked_in_ad_program.startDatetime,
      endDatetime: program.linked_in_ad_program.endDatetime,
      totalBudget: program.linked_in_ad_program.totalBudget,
      monthlyBudget: program.linked_in_ad_program.monthlyBudget,
      leadGenForm: program.linked_in_ad_program.leadGenFormUrn ?? null,
      destinationUrl: program.linked_in_ad_program.destinationUrl ?? null,
      adFormat: {
        type: program.linked_in_ad_format.type as
          | "SPONSORED_CONTENT"
          | "SPONSORED_CONVERSATION"
          | "SPONSORED_INMAIL",
        format: program.linked_in_ad_format.format as "SINGLE_IMAGE",
      } as LinkedInAdFormat,
      dateCreated: program.linked_in_ad_program.dateCreated,
      status: program.linked_in_ad_program.status ?? "ARCHIVED_DRAFT",
      type: program.linked_in_ad_program.type ?? "EVENT_DRIVEN",
    });
  }
}
