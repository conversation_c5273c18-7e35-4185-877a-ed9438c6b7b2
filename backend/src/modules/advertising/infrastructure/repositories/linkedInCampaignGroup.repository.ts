import { and, eq, gte, inArray, isNull, lt, or } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInAdAccountTable } from "../../../../database/schemas/advertising/linkedInAdAccount.table";
import { linkedInAdFormatTable } from "../../../../database/schemas/advertising/linkedInAdFormat.table";
import { linkedInAdProgramTable } from "../../../../database/schemas/advertising/linkedInAdProgram.table";
import { linkedInAdSegmentTable } from "../../../../database/schemas/advertising/linkedInAdSegment.table";
import { linkedInCampaignGroupTable } from "../../../../database/schemas/advertising/linkedInCampaignGroup.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ILinkedInCampaignGroupRepository } from "../../application/interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";
import { AdProgram } from "../../domain/entites/adProgram";
import {
  linkedInCampaignGroup,
  LinkedInCampaignGroup,
} from "../../domain/entites/linkedInCampaignGroup";
import { LinkedInAdFormat } from "../../domain/valueObjects/linkedinAdFormat";

export class LinkedInCampaignGroupRepository
  implements ILinkedInCampaignGroupRepository
{
  async getForOrganizationInTimeRange(
    organizationId: number,
    timeRange: {
      startDate: Date;
      endDate: Date;
    },
    tx?: Transaction,
  ): Promise<LinkedInCampaignGroup[]> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(linkedInCampaignGroupTable)
      .innerJoin(
        linkedInAdSegmentTable,
        eq(
          linkedInCampaignGroupTable.linkedInAdSegmentId,
          linkedInAdSegmentTable.id,
        ),
      )
      .innerJoin(
        linkedInAdProgramTable,
        eq(
          linkedInAdSegmentTable.linkedInAdProgramId,
          linkedInAdProgramTable.id,
        ),
      )
      .innerJoin(
        linkedInAdAccountTable,
        eq(
          linkedInAdProgramTable.linkedInAdAccountId,
          linkedInAdAccountTable.id,
        ),
      )
      .where(
        and(
          eq(linkedInAdAccountTable.organizationId, organizationId),
          lt(linkedInAdProgramTable.startDatetime, timeRange.endDate),
          or(
            gte(linkedInAdProgramTable.endDatetime, timeRange.startDate),
            isNull(linkedInAdProgramTable.endDatetime),
          ),
        ),
      );

    return result.map((r) => linkedInCampaignGroup(r.linkedin_campaign_group));
  }
  async createOne(
    linkedInCampaignGroup: LinkedInCampaignGroup,
    tx?: Transaction,
  ): Promise<LinkedInCampaignGroup> {
    const invoker = tx ?? db;
    await invoker
      .insert(linkedInCampaignGroupTable)
      .values(linkedInCampaignGroup);
    return linkedInCampaignGroup;
  }
  async getOneByAdSegmentId(
    id: string,
    tx?: Transaction,
  ): Promise<LinkedInCampaignGroup | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(linkedInCampaignGroupTable)
      .where(eq(linkedInCampaignGroupTable.linkedInAdSegmentId, id));
    if (!result[0]) {
      return null;
    }
    return linkedInCampaignGroup(result[0]);
  }
  async getOneByLinkedInUrn(
    linkedInUrn: string,
    tx?: Transaction,
  ): Promise<LinkedInCampaignGroup | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(linkedInCampaignGroupTable)
      .where(
        eq(linkedInCampaignGroupTable.linkedInCampaignGroupUrn, linkedInUrn),
      );
    if (!result[0]) {
      return null;
    }
    return linkedInCampaignGroup(result[0]);
  }
  async updateStatusById(
    id: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInCampaignGroupTable)
      .set({ status: status })
      .where(eq(linkedInCampaignGroupTable.linkedInAdSegmentId, id));
  }
  async updateStatusByLinkedInUrn(
    linkedInUrn: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInCampaignGroupTable)
      .set({ status: status })
      .where(
        eq(linkedInCampaignGroupTable.linkedInCampaignGroupUrn, linkedInUrn),
      );
  }

  async getManyByLinkedInAdSegmentIds(
    linkedInAdSegmentIds: string[],
    tx?: Transaction,
  ) {
    if (linkedInAdSegmentIds.length === 0) {
      return [];
    }

    const invoker = tx ?? db;

    const result = await invoker
      .select()
      .from(linkedInCampaignGroupTable)
      .where(
        inArray(
          linkedInCampaignGroupTable.linkedInAdSegmentId,
          linkedInAdSegmentIds,
        ),
      );

    return result;
  }

  async getAllForLinkedInAdAccountId(
    linkedInAdAccountId: string,
    tx?: Transaction,
  ): Promise<LinkedInCampaignGroup[]> {
    const invoker = tx ?? db;

    const result = await invoker
      .select()
      .from(linkedInCampaignGroupTable)
      .innerJoin(
        linkedInAdSegmentTable,
        eq(
          linkedInCampaignGroupTable.linkedInAdSegmentId,
          linkedInAdSegmentTable.id,
        ),
      )
      .innerJoin(
        linkedInAdProgramTable,
        eq(
          linkedInAdSegmentTable.linkedInAdProgramId,
          linkedInAdProgramTable.id,
        ),
      )
      .where(
        eq(linkedInAdProgramTable.linkedInAdAccountId, linkedInAdAccountId),
      );

    return result.map((r) => linkedInCampaignGroup(r.linkedin_campaign_group));
  }

  async getAllForLinkedInAdAccountIdWithAdProgram(
    linkedInAdAccountId: string,
    status?: (
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED"
    )[],
    tx?: Transaction,
  ): Promise<{ campaignGroup: LinkedInCampaignGroup; adProgram: AdProgram }[]> {
    const invoker = tx ?? db;

    let filter;

    if (status) {
      filter = and(
        eq(linkedInAdProgramTable.linkedInAdAccountId, linkedInAdAccountId),
        inArray(linkedInCampaignGroupTable.status, status),
      );
    } else {
      filter = eq(
        linkedInAdProgramTable.linkedInAdAccountId,
        linkedInAdAccountId,
      );
    }

    const result = await invoker
      .select({
        campaignGroup: linkedInCampaignGroupTable,
        adProgram: linkedInAdProgramTable,
        adFormat: linkedInAdFormatTable,
      })
      .from(linkedInCampaignGroupTable)
      .innerJoin(
        linkedInAdSegmentTable,
        eq(
          linkedInCampaignGroupTable.linkedInAdSegmentId,
          linkedInAdSegmentTable.id,
        ),
      )
      .innerJoin(
        linkedInAdProgramTable,
        eq(
          linkedInAdSegmentTable.linkedInAdProgramId,
          linkedInAdProgramTable.id,
        ),
      )
      .innerJoin(
        linkedInAdFormatTable,
        eq(linkedInAdFormatTable.id, linkedInAdProgramTable.adFormatId),
      )
      .where(filter);

    return result.map((r) => {
      return {
        adProgram: AdProgram({
          id: r.adProgram.id,
          linkedInAdAccountId: r.adProgram.linkedInAdAccountId,
          title: r.adProgram.title,
          objectiveType: r.adProgram.objectiveType,
          startDatetime: r.adProgram.startDatetime,
          endDatetime: r.adProgram.endDatetime,
          totalBudget: r.adProgram.totalBudget,
          monthlyBudget: r.adProgram.monthlyBudget,
          leadGenForm: r.adProgram.leadGenFormUrn ?? null,
          destinationUrl: r.adProgram.destinationUrl ?? null,
          adFormat: {
            type: r.adFormat.type,
            format: r.adFormat.format,
          } as LinkedInAdFormat,
          dateCreated: r.adProgram.dateCreated,
          status: r.adProgram.status ?? "ARCHIVED_DRAFT",
          type: r.adProgram.type ?? "EVENT_DRIVEN",
        }),
        campaignGroup: linkedInCampaignGroup(r.campaignGroup),
      };
    });
  }

  async updateBudgetByAdSegmentId(
    adSegmentId: string,
    totalBudget: number,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInCampaignGroupTable)
      .set({ totalBudget: totalBudget })
      .where(eq(linkedInCampaignGroupTable.linkedInAdSegmentId, adSegmentId));
  }
}
