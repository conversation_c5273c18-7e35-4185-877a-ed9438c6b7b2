import { subMonths } from "date-fns";
import { eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInAdSegmentTable } from "../../../../database/schemas/advertising/linkedInAdSegment.table";
import { linkedInAudienceTable } from "../../../../database/schemas/advertising/linkedInAudience.table";
import { linkedInCampaignTable } from "../../../../database/schemas/advertising/linkedInCampaign.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { LinkedInCampaign } from "../../domain/entites/linkedInCampaign";

export class LinkedInCampaignRepository
  implements ILinkedInCampaignRepositoryInterface
{
  async getManyForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<LinkedInCampaign[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select({
        linkedInAudienceId: linkedInAudienceTable.id,
        linkedInCampaignUrn: linkedInCampaignTable.linkedInCampaignUrn,
        totalBudget: linkedInCampaignTable.totalBudget,
        status: linkedInCampaignTable.status,
      })
      .from(linkedInCampaignTable)
      .innerJoin(
        linkedInAudienceTable,
        eq(linkedInAudienceTable.id, linkedInCampaignTable.linkedInAudienceId),
      )
      .where(eq(linkedInAudienceTable.linkedInAdSegmentId, adSegmentId));
    return res.map((campaign) =>
      LinkedInCampaign({
        linkedInAudienceId: campaign.linkedInAudienceId,
        linkedInCampaignUrn: campaign.linkedInCampaignUrn,
        totalBudget: campaign.totalBudget,
        status: campaign.status,
      }),
    );
  }
  async createOne(input: LinkedInCampaign, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker.insert(linkedInCampaignTable).values(input);
  }
  async getOneById(
    audienceId: string,
    tx?: Transaction,
  ): Promise<LinkedInCampaign | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(linkedInCampaignTable)
      .where(eq(linkedInCampaignTable.linkedInAudienceId, audienceId));
    if (!result[0]) {
      return null;
    }
    return LinkedInCampaign({
      linkedInAudienceId: result[0].linkedInAudienceId,
      linkedInCampaignUrn: result[0].linkedInCampaignUrn,
      totalBudget: result[0].totalBudget,
      status: result[0].status,
    });
  }
  async getOneByLinkedInCampaignUrn(
    linkedInCampaignUrn: string,
    tx?: Transaction,
  ): Promise<LinkedInCampaign | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(linkedInCampaignTable)
      .where(
        eq(linkedInCampaignTable.linkedInCampaignUrn, linkedInCampaignUrn),
      );
    if (!result[0]) {
      return null;
    }
    return LinkedInCampaign({
      linkedInAudienceId: result[0].linkedInAudienceId,
      linkedInCampaignUrn: result[0].linkedInCampaignUrn,
      totalBudget: result[0].totalBudget,
      status: result[0].status,
    });
  }
  async updateStatusByUrn(
    linkedInCampaignUrn: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInCampaignTable)
      .set({ status: status })
      .where(
        eq(linkedInCampaignTable.linkedInCampaignUrn, linkedInCampaignUrn),
      );
  }
  async updateStatusById(
    id: string,
    status:
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInCampaignTable)
      .set({ status: status })
      .where(eq(linkedInCampaignTable.linkedInAudienceId, id));
  }

  async getManyByIds(
    ids: string[],
    tx?: Transaction,
  ): Promise<LinkedInCampaign[]> {
    if (ids.length === 0) {
      return [];
    }
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(linkedInCampaignTable)
      .where(inArray(linkedInCampaignTable.linkedInAudienceId, ids));
    return result.map((campaign) =>
      LinkedInCampaign({
        linkedInAudienceId: campaign.linkedInAudienceId,
        linkedInCampaignUrn: campaign.linkedInCampaignUrn,
        totalBudget: campaign.totalBudget,
        status: campaign.status,
      }),
    );
  }

  async updateBudgetByUrn(
    linkedInCampaignUrn: string,
    totalBudget: number,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInCampaignTable)
      .set({ totalBudget: totalBudget })
      .where(
        eq(linkedInCampaignTable.linkedInCampaignUrn, linkedInCampaignUrn),
      );
  }
}
