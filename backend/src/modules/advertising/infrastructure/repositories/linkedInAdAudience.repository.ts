import { and, eq, inArray, sql } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInAdAccountTable } from "../../../../database/schemas/advertising/linkedInAdAccount.table";
import { linkedInAdProgramTable } from "../../../../database/schemas/advertising/linkedInAdProgram.table";
import { linkedInAdSegmentTable } from "../../../../database/schemas/advertising/linkedInAdSegment.table";
import { linkedInAudienceTable } from "../../../../database/schemas/advertising/linkedInAudience.table";
import { linkedInAudienceExcludeEntityTable } from "../../../../database/schemas/advertising/linkedInAudienceTargeting/linkedInAudienceCriteria/linkedInAudienceExcludeEntity.table";
import { linkedInAudienceOrGroupTable } from "../../../../database/schemas/advertising/linkedInAudienceTargeting/linkedInAudienceCriteria/linkedInAudienceOrGroup.table";
import { linkedInAudienceOrGroupEntityTable } from "../../../../database/schemas/advertising/linkedInAudienceTargeting/linkedInAudienceCriteria/linkedInAudienceOrGroupEntity.table";
import { linkedInAudienceEntityTable } from "../../../../database/schemas/advertising/linkedInAudienceTargeting/linkedInAudienceEntity.table";
import { linekdInFacetTable } from "../../../../database/schemas/advertising/linkedInAudienceTargeting/linkedInFacet.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { ILinkedInAdAudienceRepository } from "../../application/interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { AdAudience } from "../../domain/entites/AdAudience";
import { LinkedInAudienceTargetCriteria } from "../../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";

export class LinkedInAdAudienceRepository
  implements ILinkedInAdAudienceRepository {
  async createOne(
    adAudience: AdAudience,
    tx: Transaction,
  ): Promise<AdAudience> {
    const invoker = tx ?? db;
    await invoker.insert(linkedInAudienceTable).values({
      id: adAudience.id,
      linkedInAdSegmentId: adAudience.linkedInAdSegmentId,
      audiencePopulated: adAudience.audiencePopulated,
      toUse: adAudience.toBeUsed,
    });

    if (adAudience.audienceTargetCriteria.include.and.length > 0) {
      await this.updateAudienceTargetCriteria(
        adAudience.id,
        adAudience.audienceTargetCriteria,
        tx,
      );
    }

    return adAudience;
  }
  async deleteMany(ids: string[], tx?: Transaction): Promise<void> {
    if (ids.length === 0) {
      return;
    }
    const invoker = tx ?? db;
    for (const id of ids) {
      await this.deleteAudienceTargets(id, tx);
    }
    await invoker
      .delete(linkedInAudienceTable)
      .where(inArray(linkedInAudienceTable.id, ids));
  }
  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<Omit<AdAudience, "audienceTargetCriteria">[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInAudienceTable)
      .where(eq(linkedInAudienceTable.linkedInAdSegmentId, adSegmentId));

    return res.map((r) => ({
      id: r.id,
      linkedInAdSegmentId: r.linkedInAdSegmentId,
      toBeUsed: r.toUse,
      audiencePopulated: r.audiencePopulated ?? false,
    }));
  }
  async getManyForManyAdSegments(
    adSegmentIds: string[],
    tx?: Transaction,
  ): Promise<Omit<AdAudience, "audienceTargetCriteria">[]> {
    if (adSegmentIds.length === 0) {
      return [];
    }
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInAudienceTable)
      .where(inArray(linkedInAudienceTable.linkedInAdSegmentId, adSegmentIds));

    return res.map((r) => ({
      id: r.id,
      linkedInAdSegmentId: r.linkedInAdSegmentId,
      toBeUsed: r.toUse,
      audiencePopulated: r.audiencePopulated ?? false,
    }));
  }
  async getOne(id: string, tx?: Transaction): Promise<AdAudience | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInAudienceTable)
      .where(eq(linkedInAudienceTable.id, id))
      .limit(1);

    if (!res[0]) {
      return null;
    }
    return AdAudience({
      id: res[0].id,
      linkedInAdSegmentId: res[0].linkedInAdSegmentId,
      toBeUsed: res[0].toUse,
      audiencePopulated: res[0].audiencePopulated ?? false,
      audienceTargetCriteria: await this.getAudienceTargetingCriteria(
        res[0].id,
        tx,
      ),
    });
  }

  private async deleteExludes(adAudienceId: string, tx?: Transaction) {
    const invoker = tx ?? db;
    await invoker
      .delete(linkedInAudienceExcludeEntityTable)
      .where(
        eq(linkedInAudienceExcludeEntityTable.linkedInAudienceId, adAudienceId),
      );
  }

  private async updateExludes(
    adAudienceId: string,
    targetCriteria: LinkedInAudienceTargetCriteria,
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;
    await this.deleteExludes(adAudienceId, tx);
    if (!targetCriteria.exclude || targetCriteria.exclude.or.length === 0) {
      return;
    }

    const facetsInDb = await invoker
      .select()
      .from(linekdInFacetTable)
      .where(
        inArray(
          linekdInFacetTable.facetUrn,
          targetCriteria.exclude.or.map((facet) => facet.facetUrn),
        ),
      );

    const facetsNotInDb = targetCriteria.exclude.or.filter(
      (facet) => !facetsInDb.find((f) => f.facetUrn === facet.facetUrn),
    );

    if (facetsNotInDb.length > 0) {
      await invoker.insert(linekdInFacetTable)
        .values(
          facetsNotInDb.map((facet) => ({
            facetUrn: facet.facetUrn,
            facetName: facet.facetName,
          })),
        )
        .onConflictDoUpdate({
          target: linekdInFacetTable.facetUrn,
          set: {
            facetName: sql`excluded.facet_name`,
          },
        });
    }

    const excludeEntities: {
      id: string;
      entityUrn: string;
      entityName: string;
      facetUrn: string;
      facetName: string;
    }[] = [];

    for (const facet of targetCriteria.exclude.or) {
      for (const entity of facet.facetEntites) {
        excludeEntities.push({
          id: createUuid(),
          entityUrn: entity.entityUrn,
          entityName: entity.entityName,
          facetUrn: facet.facetUrn,
          facetName: facet.facetName,
        });
      }
    }

    const entitiesInDb = await invoker
      .select()
      .from(linkedInAudienceEntityTable)
      .where(
        inArray(
          linkedInAudienceEntityTable.entityUrn,
          excludeEntities.map((excludeEntity) => excludeEntity.entityUrn),
        ),
      );

    const entitiesNotInDb = excludeEntities.filter(
      (excludeEntity) =>
        !entitiesInDb.find((e) => e.entityUrn === excludeEntity.entityUrn),
    );

    if (entitiesNotInDb.length > 0) {
      await invoker.insert(linkedInAudienceEntityTable)
        .values(
          entitiesNotInDb.map((entity) => ({
            entityUrn: entity.entityUrn,
            entityName: entity.entityName,
            facetUrn: entity.facetUrn,
          })),
        )
        .onConflictDoNothing();
    }

    await invoker.insert(linkedInAudienceExcludeEntityTable).values(
      excludeEntities.map((excludeEntity) => ({
        id: excludeEntity.id,
        linkedInAudienceId: adAudienceId,
        linkedInAudienceEntityUrn: excludeEntity.entityUrn,
      })),
    );
  }

  async updateAudienceTargetCriteria(
    adAudienceId: string,
    targetCriteria: LinkedInAudienceTargetCriteria,
    tx: Transaction,
  ) {
    const invoker = tx ?? db;
    const existingAudienceOrGroups = await invoker
      .select()
      .from(linkedInAudienceOrGroupTable)
      .where(eq(linkedInAudienceOrGroupTable.linkedInAudienceId, adAudienceId));

    if (existingAudienceOrGroups.length > 0) {
      await invoker.delete(linkedInAudienceOrGroupEntityTable).where(
        inArray(
          linkedInAudienceOrGroupEntityTable.linkedInAudienceOrGroupId,
          existingAudienceOrGroups.map((orGroup) => orGroup.id),
        ),
      );
      await invoker.delete(linkedInAudienceOrGroupTable).where(
        inArray(
          linkedInAudienceOrGroupTable.id,
          existingAudienceOrGroups.map((orGroup) => orGroup.id),
        ),
      );
    }

    const orGroupsWithId = targetCriteria.include.and.map((and) => ({
      orGroupId: createUuid(),
      ...and,
    }));

    await invoker.insert(linkedInAudienceOrGroupTable).values(
      orGroupsWithId.map((orGroup) => ({
        id: orGroup.orGroupId,
        linkedInAudienceId: adAudienceId,
      })),
    );

    const facets = targetCriteria.include.and.flatMap((and) =>
      and.or.map((or) => ({
        facetUrn: or.facetUrn,
        facetName: or.facetName,
      })),
    );
    if (facets.length > 0) {
      const facetsInDb = await invoker
        .select()
        .from(linekdInFacetTable)
        .where(
          inArray(
            linekdInFacetTable.facetUrn,
            facets.map((facet) => facet.facetUrn),
          ),
        );

      const facetsNotInDb = facets.filter(
        (facet) => !facetsInDb.find((f) => f.facetUrn === facet.facetUrn),
      );
      if (facetsNotInDb.length > 0) {
        await invoker.insert(linekdInFacetTable)
          .values(
            facetsNotInDb.map((facet) => ({
              facetUrn: facet.facetUrn,
              facetName: facet.facetName,
            })),
          )
          .onConflictDoUpdate({
            target: linekdInFacetTable.facetUrn,
            set: {
              facetName: sql`excluded.facet_name`,
            },
          });
      }
    }

    const orEntities: {
      id: string;
      orGroupId: string;
      entityUrn: string;
      facetUrn: string;
      facetName: string;
      entityName: string;
    }[] = [];

    for (const eachOrGroup of orGroupsWithId) {
      for (const facet of eachOrGroup.or) {
        for (const entity of facet.facetEntites) {
          orEntities.push({
            id: createUuid(),
            orGroupId: eachOrGroup.orGroupId,
            entityUrn: entity.entityUrn,
            facetUrn: facet.facetUrn,
            facetName: facet.facetName,
            entityName: entity.entityName,
          });
        }
      }
    }

    if (orEntities.length > 0) {
      const entitiesInDb = await invoker
        .select()
        .from(linkedInAudienceEntityTable)
        .where(
          inArray(
            linkedInAudienceEntityTable.entityUrn,
            orEntities.map((orEntity) => orEntity.entityUrn),
          ),
        );

      const entitiesNotInDb = orEntities.filter(
        (orEntity) =>
          !entitiesInDb.find((e) => e.entityUrn === orEntity.entityUrn),
      );
      if (entitiesNotInDb.length > 0) {
        await invoker.insert(linkedInAudienceEntityTable)
          .values(
            entitiesNotInDb.map((entity) => ({
              entityUrn: entity.entityUrn,
              entityName: entity.entityName,
              facetUrn: entity.facetUrn,
            })),
          )
          .onConflictDoNothing();
      }

      await invoker.insert(linkedInAudienceOrGroupEntityTable).values(
        orEntities.map((orEntity) => ({
          id: createUuid(),
          linkedInAudienceOrGroupId: orEntity.orGroupId,
          linkedInAudienceEntityUrn: orEntity.entityUrn,
        })),
      );
    }

    await this.updateExludes(adAudienceId, targetCriteria, tx);
  }

  private async getAudienceTargetingCriteria(
    adAudienceId: string,
    tx?: Transaction,
  ): Promise<LinkedInAudienceTargetCriteria> {
    const invoker = tx ?? db;
    const orGroups = await invoker
      .select()
      .from(linkedInAudienceOrGroupTable)
      .where(eq(linkedInAudienceOrGroupTable.linkedInAudienceId, adAudienceId))
      .innerJoin(
        linkedInAudienceOrGroupEntityTable,
        eq(
          linkedInAudienceOrGroupEntityTable.linkedInAudienceOrGroupId,
          linkedInAudienceOrGroupTable.id,
        ),
      )
      .innerJoin(
        linkedInAudienceEntityTable,
        eq(
          linkedInAudienceEntityTable.entityUrn,
          linkedInAudienceOrGroupEntityTable.linkedInAudienceEntityUrn,
        ),
      )
      .innerJoin(
        linekdInFacetTable,
        eq(linekdInFacetTable.facetUrn, linkedInAudienceEntityTable.facetUrn),
      );

    type OrGroupReduction = {
      orGroupId: string;
      or: {
        facetUrn: string;
        facetName: string;
        facetEntites: {
          facetUrn: string;
          entityUrn: string;
          entityName: string;
        }[];
      }[];
    };

    const orGroupsReduced = orGroups.reduce<Record<string, OrGroupReduction>>(
      (acc, row) => {
        let currOrGroup = acc[row.linked_in_audience_or_group.id];
        if (!currOrGroup) {
          currOrGroup = {
            orGroupId: row.linked_in_audience_or_group.id,
            or: [],
          };
        }

        let facet = currOrGroup.or.find(
          (f) => f.facetUrn === row.linked_in_audience_entity.facetUrn,
        );
        if (!facet) {
          facet = {
            facetUrn: row.linked_in_facet.facetUrn,
            facetName: row.linked_in_facet.facetName,
            facetEntites: [],
          };
        }
        if (
          !facet.facetEntites.find(
            (e) => e.entityUrn === row.linked_in_audience_entity.entityUrn,
          )
        ) {
          facet.facetEntites.push({
            facetUrn: row.linked_in_audience_entity.facetUrn,
            entityUrn: row.linked_in_audience_entity.entityUrn,
            entityName: row.linked_in_audience_entity.entityName,
          });
        }

        const facetsWithoutCurrentFacet = currOrGroup.or.filter(
          (f) => f.facetUrn !== facet.facetUrn,
        );
        currOrGroup.or = [...facetsWithoutCurrentFacet, facet];

        acc[row.linked_in_audience_or_group.id] = currOrGroup;
        return acc;
      },
      {},
    );

    const orGroupsAsArray = Object.values(orGroupsReduced);

    const excludeGroups = await invoker
      .select()
      .from(linkedInAudienceExcludeEntityTable)
      .where(
        eq(linkedInAudienceExcludeEntityTable.linkedInAudienceId, adAudienceId),
      )
      .innerJoin(
        linkedInAudienceEntityTable,
        eq(
          linkedInAudienceEntityTable.entityUrn,
          linkedInAudienceExcludeEntityTable.linkedInAudienceEntityUrn,
        ),
      )
      .innerJoin(
        linekdInFacetTable,
        eq(linekdInFacetTable.facetUrn, linkedInAudienceEntityTable.facetUrn),
      );

    type ExcludeGroupReduction = {
      facetUrn: string;
      facetName: string;
      facetEntites: {
        facetUrn: string;
        entityUrn: string;
        entityName: string;
      }[];
    };

    const excludeGroupsReduced = excludeGroups.reduce<
      Record<string, ExcludeGroupReduction>
    >((acc, row) => {
      let currExcludeGroup = acc[row.linked_in_facet.facetUrn];
      if (!currExcludeGroup) {
        currExcludeGroup = {
          facetUrn: row.linked_in_facet.facetUrn,
          facetName: row.linked_in_facet.facetName,
          facetEntites: [],
        };
      }

      if (
        !currExcludeGroup.facetEntites.find(
          (e) => e.entityUrn === row.linked_in_audience_entity.entityUrn,
        )
      ) {
        currExcludeGroup.facetEntites.push({
          facetUrn: row.linked_in_audience_entity.facetUrn,
          entityUrn: row.linked_in_audience_entity.entityUrn,
          entityName: row.linked_in_audience_entity.entityName,
        });
      }

      acc[row.linked_in_facet.facetUrn] = currExcludeGroup;
      return acc;
    }, {});

    const finalRes: LinkedInAudienceTargetCriteria = {
      include: {
        and: orGroupsAsArray.map((orGroup) => ({
          or: orGroup.or,
        })),
      },
      exclude:
        Object.values(excludeGroupsReduced).length > 0
          ? {
            or: Object.values(excludeGroupsReduced),
          }
          : undefined,
    };

    return finalRes;
  }

  async checkIfAudienceExistsForOrganization(
    organizationId: number,
    adAudienceId: string,
    tx?: Transaction,
  ): Promise<boolean> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInAudienceTable)
      .where(
        and(
          eq(linkedInAudienceTable.id, adAudienceId),
          eq(linkedInAdAccountTable.organizationId, organizationId),
        ),
      )
      .innerJoin(
        linkedInAdSegmentTable,
        eq(
          linkedInAdSegmentTable.id,
          linkedInAudienceTable.linkedInAdSegmentId,
        ),
      )
      .innerJoin(
        linkedInAdProgramTable,
        eq(
          linkedInAdProgramTable.id,
          linkedInAdSegmentTable.linkedInAdProgramId,
        ),
      )
      .innerJoin(
        linkedInAdAccountTable,
        eq(
          linkedInAdAccountTable.id,
          linkedInAdProgramTable.linkedInAdAccountId,
        ),
      )
      .limit(1);
    if (!res[0]) {
      return false;
    }
    return true;
  }

  private async deleteAudienceTargets(adAudienceId: string, tx?: Transaction) {
    const invoker = tx ?? db;
    await this.deleteExludes(adAudienceId, tx);
    const existingAudienceOrGroups = await invoker
      .select()
      .from(linkedInAudienceOrGroupTable)
      .where(eq(linkedInAudienceOrGroupTable.linkedInAudienceId, adAudienceId));

    if (existingAudienceOrGroups.length > 0) {
      await invoker.delete(linkedInAudienceOrGroupEntityTable).where(
        inArray(
          linkedInAudienceOrGroupEntityTable.linkedInAudienceOrGroupId,
          existingAudienceOrGroups.map((orGroup) => orGroup.id),
        ),
      );
      await invoker.delete(linkedInAudienceOrGroupTable).where(
        inArray(
          linkedInAudienceOrGroupTable.id,
          existingAudienceOrGroups.map((orGroup) => orGroup.id),
        ),
      );
    }
  }

  async updateToBeUsed(
    adAudienceId: string,
    toBeUsed: boolean,
    tx: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(linkedInAudienceTable)
      .set({ toUse: toBeUsed })
      .where(eq(linkedInAudienceTable.id, adAudienceId));
  }
}
