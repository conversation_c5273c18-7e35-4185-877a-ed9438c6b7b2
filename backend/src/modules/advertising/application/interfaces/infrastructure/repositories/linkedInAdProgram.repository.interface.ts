import { z } from "zod";

import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import {
  AdProgram,
  adProgramSchema,
} from "../../../../domain/entites/adProgram";

export interface LinkedInAdProgramRepositoryInterface {
  createOne(input: AdProgram, tx?: ITransaction): Promise<AdProgram>;
  getOne(id: string): Promise<AdProgram | null>;
  getByIds(ids: string[], tx?: ITransaction): Promise<AdProgram[]>;
  updateOne(input: AdProgram, tx?: ITransaction): Promise<AdProgram>;
  checkIfAdProgramBelongsToOrganization(
    adProgramId: string,
    organizationId: number,
    tx?: ITransaction,
  ): Promise<boolean>;
  getForOrganization(organizationId: number): Promise<AdProgram[]>;
  getFilteredCampaigns(
    organizationId: number,
    status?: string,
    adAccountId?: string,
  ): Promise<AdProgram[]>;
  getByAdSegmentId(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<AdProgram | null>;
}
