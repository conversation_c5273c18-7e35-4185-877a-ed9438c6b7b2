import { ILLMCompletionService } from "../../../../../core/application/interfaces/infrastructure/services/llmCompletionService.interface";
import { IPromptStorageService } from "../../../../../core/application/interfaces/infrastructure/services/promptStorage.service.interface";
import { ConversationSubjectCopyType } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export interface IAdvertisingLlmCompletionsService {
  mapKalosFieldToLinkedInFacetEntity(
    field: string,
    facetEntities: string,
    fieldValue: string,
  ): Promise<string>;

  mapKalosJobTitlesToLinkedInJobTitles(
    jobTitles: string[],
    jobFunction: string,
    jobSeniority: string,
    facetEntities: string,
  ): Promise<string>;
  generateAudienceKeyPointsFromPositioning(input: {
    positioning: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;
  generateUseCasesFromCaseStudies(input: {
    caseStudies: string[];
    targetAudience: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;

  modifyAdCopyToFitSocialPostStyleGuide(input: {
    adCopy: string;
    positioningKeyPoints: string;
    exampleSocialPost: string[];
    organizationName: string;
    useCases: string;
    styleGuide: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;
  generateAdSegmentSocialPostBaseCopy(input: {
    positioningKeyPoints: string;
    exampleSocialPost: string[];
    organizationName: string;
    useCases: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;

  generateAdSegmentConversationBaseCopy(input: {
    positioningKeyPoints: string;
    organizationName: string;
    useCases: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;

  generateConversationBody(
    input: {
      baseCopy: string;
      adSegmentValueProp: string;
      subject: string;
      positioning: string;
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateSocialPostBody(
    input: {
      baseCopy: string;
      adSegmentValueProp: string;
      styleGuide: string;
      exampleSocialPost: string[];
      positioning: string;
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  getnerateConversationTitle(
    input: {
      company: string;
      adSegmentValueProp: string;
      subjectType: ConversationSubjectCopyType;
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateSocialPostHeadline(
    input: {
      baseCopy: string;
      adSegmentValueProp: string;
      styleGuide: string;
      exampleSocialPost: string[];
      adTargeting: {
        jobFunction?: string | null;
        jobSeniority?: string | null;
        verticals?: string[] | null;
        annualRevenueLowBound?: number | null;
        annualRevenueHighBound?: number | null;
        numberOfEmployeesLowBound?: number | null;
        numberOfEmployeesHighBound?: number | null;
      };
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateRefinedAdSegmentSocialPostBaseCopy(
    input: {
      baseCopy: string;
      feedback: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateRefinedSocialPostTitle(
    input: {
      baseCopy: string;
      feedback: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateRefinedConversationSubject(
    input: {
      baseCopy: string;
      feedback: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateRefinedConversationMessage(
    input: {
      baseCopy: string;
      feedback: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateConversationMessageCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateSocialPostBodyCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateSocialPostHeadlineCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateConversationCallToActionCopyVarient(
    input: {
      standardCopy: string;
      type: string;
    },
    ctx: {
      lllmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
    },
  ): AsyncGenerator<string>;

  generateLinkedInSkillsFromICP(input: {
    positioningKeyPoints: string;
    organizationName: string;
    productCategory: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;

  generateLinkedInGroupsFromICP(input: {
    positioningKeyPoints: string;
    organizationName: string;
    productCategory: string;
    adTargeting: {
      jobFunction?: string | null;
      jobSeniority?: string | null;
      verticals?: string[] | null;
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
    };
  }): Promise<string>;
}
