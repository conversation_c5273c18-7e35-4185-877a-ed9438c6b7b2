import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "../../../domain/services/conversationCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInAdSegmentSocialPostBaseCopyService } from "../../../domain/services/linkedInAdSegmentSocialPostBaseCopy.service";
import { LinkedInConversationBaseCopyService } from "../../../domain/services/linkedInConversationBaseCopy.service";
import { SegmentAudienceTargetPrefabService } from "../../../domain/services/segmentAudiencePrefab.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";
import { SetAdSegmentsForAdProgramRequestDto } from "../../dtos/controllerDtos/adSegment/setAdSegmentsForAdProgram.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { ISocialPostCallToActionCopyRepository } from "../../interfaces/infrastructure/repositories/socialPostCallToActionCopy.repository.interface";

export async function setAdSegmentsForAdProgramUseCase(
  input: SetAdSegmentsForAdProgramRequestDto,
  ctx: {
    adSegmentService: AdSegmentService;
    adAudienceService: AdAudienceService;
    linkedInAdProgramService: LinkedInAdProgramService;
    segmentAudiencePrefabService: SegmentAudienceTargetPrefabService;
    segmentService: SegmentService;
    organizationId: number;
    adSegmentValuePropService: AdSegmentValuePropService;
    linkedInAdSegmentSocialPostBaseCopyService: LinkedInAdSegmentSocialPostBaseCopyService;
    socialPostAdCopyService: SocialPostAdCopyService;
    conversationBaseCopyService: LinkedInConversationBaseCopyService;
    conversationCopyService: ConversationCopySerivce;
    conversationSubjectCopyService: ConversationSubjectCopyService;
    conversationMessageCopyService: ConversationMessageCopyService;
    conversationCallToActionCopyService: ConversationCallToActionCopyService;
    adSegmentSelectedConversationSubjectTypeService: AdSegmentSelectedConversationSubjectTypeService;
    adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
    socialPostCallToActionCopyRepository: ISocialPostCallToActionCopyRepository;
    tx: ITransaction;
  },
) {
  const [
    doesAdProgramBelongToOrganization,
    segmentsBelongToOrganization,
    adProgram,
  ] = await Promise.all([
    ctx.linkedInAdProgramService.checkIfAdProgramBelongsToOrganization(
      input.adProgramId,
      ctx.organizationId,
    ),
    ctx.segmentService.checkIfSegmentsBelongToOrganization(
      input.segmentIds,
      ctx.organizationId,
      {
        tx: undefined,
      },
    ),
    ctx.linkedInAdProgramService.getOne(input.adProgramId),
  ]);

  if (!adProgram) {
    throw new Error("Ad program not found");
  }

  if (!doesAdProgramBelongToOrganization) {
    throw new Error("Ad program does not belong to organization");
  }

  if (!segmentsBelongToOrganization.allBelongToOrganization) {
    throw new Error("Some segments do not belong to organization");
  }

  const { created } = await ctx.adSegmentService.setForAdProgram(
    {
      adProgramId: input.adProgramId,
      segmentIds: input.segmentIds,
    },
    ctx.adAudienceService,
    ctx.adSegmentValuePropService,
    ctx.linkedInAdSegmentSocialPostBaseCopyService,
    ctx.socialPostAdCopyService,
    ctx.conversationBaseCopyService,
    ctx.conversationCopyService,
    ctx.conversationSubjectCopyService,
    ctx.conversationMessageCopyService,
    ctx.conversationCallToActionCopyService,
    ctx.adSegmentSelectedConversationSubjectTypeService,
    ctx.adSegmentValuePropCreativeRepository,
    ctx.socialPostCallToActionCopyRepository,
    ctx.tx,
  );

  for (const segment of created) {
    const prefab = await ctx.segmentAudiencePrefabService.getForSegment(
      segment.segmentId,
    );

    if (prefab.audiences.length === 0) {
      await advertisingInngestClient.send({
        name: "linkedin/create-audience",
        data: {
          adSegmentId: segment.id,
          organizationId: ctx.organizationId,
        },
      });
      console.log(`🔄 Segment ${segment.segmentId} has no prefabs - triggering audience creation`);
    } else {
      console.log(`✅ Segment ${segment.segmentId} has ${prefab.audiences.length} prefabs - creating audiences`);

      for (const audience of prefab.audiences) {
        await ctx.adAudienceService.createOne(
          {
            linkedInAdSegmentId: segment.id,
            toBeUsed: true,
            audiencePopulated: true,
            audienceTargetCriteria: audience,
          },
          ctx.tx,
        );
      }
      await ctx.adSegmentService.setReady(segment.id, true, ctx.tx);
    }
  }

  return {
    created,
  };
}
