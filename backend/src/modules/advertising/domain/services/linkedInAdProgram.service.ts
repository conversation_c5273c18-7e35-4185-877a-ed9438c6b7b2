import { LinkedInAdProgramRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { AdProgram } from "../entites/adProgram";

export class LinkedInAdProgramService {
  constructor(
    private readonly linkedInAdProgramRepository: LinkedInAdProgramRepositoryInterface,
  ) {}

  buildAdProgram(input: AdProgram) {
    return AdProgram(input);
  }

  async createOne(input: AdProgram) {
    const adProgram = this.buildAdProgram(input);
    const res = await this.linkedInAdProgramRepository.createOne(adProgram);
    return this.buildAdProgram(res);
  }

  async getOne(id: string) {
    const res = await this.linkedInAdProgramRepository.getOne(id);
    return res;
  }

  async getByIds(ids: string[]) {
    const res = await this.linkedInAdProgramRepository.getByIds(ids);
    return res;
  }

  async updateOne(input: AdProgram) {
    const adProgram = this.buildAdProgram(input);
    const res = await this.linkedInAdProgramRepository.updateOne(adProgram);
    return this.buildAdProgram(res);
  }

  async checkIfAdProgramBelongsToOrganization(
    adProgramId: string,
    organizationId: number,
  ) {
    const res =
      await this.linkedInAdProgramRepository.checkIfAdProgramBelongsToOrganization(
        adProgramId,
        organizationId,
      );
    return res;
  }

  async getForOrganization(organizationId: number) {
    const res =
      await this.linkedInAdProgramRepository.getForOrganization(organizationId);
    return res;
  }

  async getFilteredCampaigns(
    organizationId: number,
    status?: string,
    adAccountId?: string,
    fromDate?: Date,
    toDate?: Date,
  ) {
    const res = await this.linkedInAdProgramRepository.getFilteredCampaigns(
      organizationId,
      status,
      adAccountId,
    );
    return res;
  }

  async getByAdSegmentId(adSegmentId: string) {
    const res = await this.linkedInAdProgramRepository.getByAdSegmentId(adSegmentId);
    return res;
  }
}
