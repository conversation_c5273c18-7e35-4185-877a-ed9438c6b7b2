{"name": "backend", "scripts": {"dev": "tsx watch src/index.ts", "push": "pnpm drizzle-kit push --config=src/database/config.ts", "build": "tsc && node buildNode.cjs"}, "dependencies": {"@aws-sdk/client-s3": "^3.669.0", "@aws-sdk/client-sqs": "^3.670.0", "@aws-sdk/s3-request-presigner": "^3.669.0", "@clerk/backend": "^1.25.2", "@hono/clerk-auth": "^2.0.0", "@hono/node-server": "^1.13.7", "@hono/trpc-server": "^0.3.4", "@hubspot/api-client": "^12.0.1", "@kalos/api": "workspace:*", "@kalos/core": "workspace:*", "@kalos/database": "workspace:*", "@kalos/linkedin-api": "workspace:*", "@kalos/llms": "workspace:*", "@pinecone-database/pinecone": "^2.2.1", "@sinclair/typebox": "^0.32.30", "@trpc/server": "11.0.0-rc.364", "@types/pg": "^8.11.10", "dotenv": "^16.4.5", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.7.1", "hono": "^4.6.16", "inngest": "^3.19.0", "jstat": "^1.9.6", "langfuse": "^3.12.2", "neverthrow": "^8.2.0", "node-abort-controller": "^3.1.1", "node-fetch": "^3.3.2", "openai": "^4.42.0", "pg": "^8.13.1", "posthog-node": "^5.1.0", "simple-statistics": "^7.8.8", "stripe": "^17.7.0", "superjson": "2.2.1", "zod": "^3.23.0", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@kalos/eslint-config": "workspace:*", "@kalos/prettier-config": "workspace:*", "@kalos/tsconfig": "workspace:*", "@types/node": "^20.11.17", "drizzle-kit": "^0.30.6", "eslint": "^9.0.0", "prettier": "^3.2.5", "tsx": "^4.7.1", "typescript": "^5.4.5"}}