"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { api } from "@/trpc/client";
import {
  CheckCircle2,
  CheckIcon,
  InfoIcon,
  Loader2,
  Trash2,
  XCircle,
} from "lucide-react";

import { But<PERSON> } from "@kalos/ui/button";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@kalos/ui/tooltip";


// Standard UTM field keys
const STANDARD_UTM_FIELDS = [
  "utm_source",
  "utm_medium",
  "utm_campaign",
  "utm_content",
  "utm_term",
];

// Helper to format standard UTM keys to more readable labels
const formatUtmKeyToLabel = (key: string) => {
  return (
    key
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ") + " Field Name (e.g., UTM_Source__c)"
  );
};

function useHasMounted() {
  const [hasMounted, setHasMounted] = useState(false);
  useEffect(() => {
    setHasMounted(true);
  }, []);
  return hasMounted;
}

export function ConnectSalesforce() {
  const hasMounted = useHasMounted();
  const [utmFieldValues, setUtmFieldValues] = useState<Record<string, string>>(
    {},
  );
  const [mutationStatus, setMutationStatus] = useState<
    "idle" | "success" | "error"
  >("idle");

  // First meeting mapping state
  const [firstMeetingMapping, setFirstMeetingMapping] = useState({
    entity: "",
    field: "",
    type: "",
  });
  const [firstMeetingMutationStatus, setFirstMeetingMutationStatus] = useState<
    "idle" | "success" | "error"
  >("idle");

  const hubspotCredentialQuery =
    api.v2.crm.crmCredential.doesOrganizationHaveHubspotCredential.useQuery();
  const salesforceCredentialsQuery =
    api.v2.crm.crmCredential.doesOrganizationHaveSalesforceCredential.useQuery();

  // Fetch suggested UTM fields from Salesforce Lead schema
  const utmFieldSuggestionsQuery =
    api.v2.crm.salesforce.suggestLeadUtmFields.useQuery(undefined, {
      enabled:
        !!salesforceCredentialsQuery.data
          ?.doesOrganizationHaveSalesforceCredential,
      refetchOnWindowFocus: false, // Don't need constant refetching
      staleTime: Infinity, // Data is unlikely to change often
    });

  const existingUtmMappingsQuery = api.v2.crm.crmMapping.getUtmMapping.useQuery(
    { crmType: "salesforce" },
    {
      enabled:
        !!salesforceCredentialsQuery.data
          ?.doesOrganizationHaveSalesforceCredential,
      // onSuccess logic will be handled by useEffect below
    },
  );

  // First meeting mapping queries
  const existingFirstMeetingMappingQuery =
    api.v2.crm.crmMapping.getFirstMeetingMapping.useQuery(
      { crmType: "salesforce" },
      {
        enabled:
          !!salesforceCredentialsQuery.data
            ?.doesOrganizationHaveSalesforceCredential,
      },
    );

  const firstMeetingMappingDefinitionQuery =
    api.v2.crm.crmMapping.getFirstMeetingMappingDefinition.useQuery(undefined, {
      enabled:
        !!salesforceCredentialsQuery.data
          ?.doesOrganizationHaveSalesforceCredential,
    });

  useEffect(() => {
    if (existingUtmMappingsQuery.data) {
      setUtmFieldValues(existingUtmMappingsQuery.data);
    } else if (
      salesforceCredentialsQuery.data
        ?.doesOrganizationHaveSalesforceCredential &&
      !existingUtmMappingsQuery.isLoading &&
      !existingUtmMappingsQuery.isFetching
    ) {
      // If connected and query is done fetching but returned no data (null/undefined)
      const initialFields: Record<string, string> = {};
      STANDARD_UTM_FIELDS.forEach((key) => {
        initialFields[key] = utmFieldValues[key] || ""; // Preserve any half-filled state or default to empty
      });
      setUtmFieldValues(initialFields);
    }
  }, [
    existingUtmMappingsQuery.data,
    existingUtmMappingsQuery.isLoading,
    existingUtmMappingsQuery.isFetching,
    salesforceCredentialsQuery.data?.doesOrganizationHaveSalesforceCredential,
  ]);

  // Effect to populate first meeting mapping state
  useEffect(() => {
    if (existingFirstMeetingMappingQuery.data) {
      setFirstMeetingMapping({
        entity: existingFirstMeetingMappingQuery.data.entity,
        field: existingFirstMeetingMappingQuery.data.field,
        type: existingFirstMeetingMappingQuery.data.type,
      });
    }
  }, [existingFirstMeetingMappingQuery.data]);

  // Effect to pre-fill empty fields with suggestions when they load
  useEffect(() => {
    if (utmFieldSuggestionsQuery.data) {
      const suggestions = utmFieldSuggestionsQuery.data;
      const updates: Record<string, string> = {};
      let changed = false;

      STANDARD_UTM_FIELDS.forEach((key) => {
        const suggestion = suggestions[key];
        // Only update if there's a suggestion AND the current field value is empty
        if (suggestion && !utmFieldValues[key]?.trim()) {
          updates[key] = suggestion;
          changed = true;
        }
      });

      if (changed) {
        setUtmFieldValues((prev) => ({ ...prev, ...updates }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- run only when suggestions load
  }, [utmFieldSuggestionsQuery.data]);

  const saveUtmMappingsMutation =
    api.v2.crm.crmMapping.saveUtmMapping.useMutation({
      onMutate: () => {
        setMutationStatus("idle"); // Reset status on new attempt
      },
      onSuccess: () => {
        setMutationStatus("success");
        existingUtmMappingsQuery.refetch();
        // Clear success message after 3 seconds
        setTimeout(() => setMutationStatus("idle"), 3000);
      },
      onError: (error: any) => {
        setMutationStatus("error");
        console.error("Error saving UTM mappings:", error); // Log error for debugging
        // Clear error message after 3 seconds
        setTimeout(() => setMutationStatus("idle"), 3000);
      },
    });

  // First meeting mapping mutation
  const saveFirstMeetingMappingMutation =
    api.v2.crm.crmMapping.saveFirstMeetingMapping.useMutation({
      onMutate: () => {
        setFirstMeetingMutationStatus("idle");
      },
      onSuccess: () => {
        setFirstMeetingMutationStatus("success");
        existingFirstMeetingMappingQuery.refetch();
        setTimeout(() => setFirstMeetingMutationStatus("idle"), 3000);
      },
      onError: (error: any) => {
        setFirstMeetingMutationStatus("error");
        console.error("Error saving first meeting mapping:", error);
        setTimeout(() => setFirstMeetingMutationStatus("idle"), 3000);
      },
    });

  // Effect to initialize fields if salesforce is connected and existing mappings are not yet loaded or are null
  useEffect(() => {
    if (
      salesforceCredentialsQuery.data
        ?.doesOrganizationHaveSalesforceCredential &&
      !existingUtmMappingsQuery.data &&
      !existingUtmMappingsQuery.isLoading
    ) {
      const initialFields: Record<string, string> = {};
      STANDARD_UTM_FIELDS.forEach((key) => {
        initialFields[key] = utmFieldValues[key] || ""; // Preserve existing state if any, otherwise empty
      });
      setUtmFieldValues(initialFields);
    }
  }, [
    salesforceCredentialsQuery.data,
    existingUtmMappingsQuery.data,
    existingUtmMappingsQuery.isLoading,
  ]);

  const handleInputChange = (fieldName: string, value: string) => {
    setUtmFieldValues((prev) => ({ ...prev, [fieldName]: value }));
  };

  const handleSaveMappings = () => {
    // Simplified validation: ensure all standard fields are present in the state, even if empty
    const mappingsToSave: Record<string, string> = {};
    STANDARD_UTM_FIELDS.forEach((field) => {
      mappingsToSave[field] = utmFieldValues[field] || ""; // Send all standard fields, even if some are empty strings
    });

    saveUtmMappingsMutation.mutate({
      crmType: "salesforce",
      mappings: mappingsToSave, // Send all standard fields, even if some are empty strings
    });
  };

  // First meeting mapping handlers
  const handleFirstMeetingInputChange = (
    field: "entity" | "field" | "type",
    value: string,
  ) => {
    setFirstMeetingMapping((prev) => ({ ...prev, [field]: value }));
  };

  const handleSaveFirstMeetingMapping = () => {
    if (
      !firstMeetingMapping.entity ||
      !firstMeetingMapping.field ||
      !firstMeetingMapping.type
    ) {
      return; // Basic validation
    }

    saveFirstMeetingMappingMutation.mutate({
      crmType: "salesforce",
      mapping: {
        type: firstMeetingMapping.type,
        field: firstMeetingMapping.field,
        entity: firstMeetingMapping.entity,
        operator: "exists", // Auto-select exists as specified
      },
    });
  };

  if (!hasMounted) return null;

  const isLoading =
    salesforceCredentialsQuery.isLoading ||
    hubspotCredentialQuery.isLoading ||
    existingUtmMappingsQuery.isLoading;
  const salesforceConnected =
    salesforceCredentialsQuery.data?.doesOrganizationHaveSalesforceCredential;
  const hubspotConnected =
    hubspotCredentialQuery.data?.doesOrganizationHaveHubspotCredential;

  return (
    <>
      {!hubspotConnected && !salesforceConnected && !isLoading && (
        <Link href="/oauth2/salesforce">
          <Button
            variant="outline"
            className="h-16 w-full justify-start"
            disabled={isLoading || salesforceConnected || hubspotConnected}
          >
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg"
              alt="Salesforce Logo"
              className="mr-2 w-12"
            />
            Salesforce
          </Button>
        </Link>
      )}
      {(salesforceConnected ||
        (salesforceCredentialsQuery.isLoading && !hubspotConnected)) && (
        <Button
          variant="outline"
          className={cn(
            salesforceConnected
              ? "disabled:bg-green-100 disabled:opacity-100"
              : "",
            "h-16 w-full justify-start",
          )}
          disabled={isLoading || salesforceConnected || hubspotConnected}
        >
          <img
            src="https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg"
            alt="Salesforce Logo"
            className="mr-2 w-12"
          />
          Salesforce
          {salesforceConnected && (
            <div className="flex w-full items-center justify-end">
              <CheckIcon />
            </div>
          )}
        </Button>
      )}
      {salesforceConnected && (
        <div>
          <div className="mt-4 space-y-4 rounded-md border bg-muted p-4">
            <h3 className="text-sm font-semibold">UTM Parameter Mapping</h3>
            <span className="text-sm text-muted-foreground">
              Enter the custom Salesforce Lead API field names for each UTM
              parameter. These are case-sensitive. If you don't have UTM
              parameters set up, set them up in Salesforce and then complete
              this form.
            </span>
            <div className="mb-3 mt-3 space-y-1 rounded-md border bg-background p-3 text-xs text-muted-foreground">
              <p className="mb-1 text-sm font-semibold">
                How to find Salesforce UTM API Field Names:
              </p>
              <ol className="list-inside list-decimal space-y-0.5 pl-2">
                <li>
                  Go into Salesforce, then navigate to <strong>Setup</strong>{" "}
                  (usually a gear icon in the top right corner).
                </li>
                <li>
                  From Setup, go to <strong>Object Manager</strong>.
                </li>
                <li>
                  In Object Manager, find and click on the <strong>Lead</strong>{" "}
                  object.
                </li>
                <li>
                  In the Lead object details, click on{" "}
                  <strong>Fields & Relationships</strong> in the sidebar.
                </li>
                <li>
                  For each UTM parameter you want to map, find the corresponding
                  field and copy its <strong>Field Name</strong> (this is the
                  API Name).
                </li>
              </ol>
            </div>
            {existingUtmMappingsQuery.isLoading && (
              <p className="pt-2 text-sm text-muted-foreground">
                Loading settings...
              </p>
            )}
            {existingUtmMappingsQuery.isError && (
              <p className="pt-2 text-sm text-red-500">
                Error loading settings: {existingUtmMappingsQuery.error.message}
              </p>
            )}

            {!existingUtmMappingsQuery.isLoading &&
              !existingUtmMappingsQuery.isError && (
                <div className="space-y-3 pt-2">
                  {STANDARD_UTM_FIELDS.map((fieldKey, index) => {
                    const label = formatUtmKeyToLabel(fieldKey).replace(
                      " Field Name (e.g., UTM_Source__c)",
                      "",
                    ); // Get just "UTM Source", etc.
                    const value = utmFieldValues[fieldKey] || "";
                    // Simplify placeholder - suggestion is now used for value if field is empty
                    const placeholder = `e.g., ${label.replace(" ", "_")}__c`;
                    const isLoadingSuggestion =
                      utmFieldSuggestionsQuery.isLoading;

                    return (
                      <div key={fieldKey} className="space-y-1">
                        <label htmlFor={fieldKey}>{label}</label>
                        {index === 0 && (
                          <TooltipProvider>
                            <Tooltip delayDuration={300}>
                              <TooltipTrigger>
                                <InfoIcon
                                  size={20}
                                  className="ml-2 inline justify-self-end"
                                />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  UTM Params are auto suggested form your
                                  Salesforce account.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}

                        <div className="relative flex items-center">
                          <Input
                            id={fieldKey}
                            name={fieldKey}
                            placeholder={
                              isLoadingSuggestion
                                ? "Loading suggestion..."
                                : placeholder
                            }
                            value={value}
                            onChange={(e) =>
                              handleInputChange(fieldKey, e.target.value)
                            }
                            className="peer pr-10" // Add padding for the icon
                            aria-label={label}
                          />
                          {/* Show loading spinner or checkmark */}
                          {isLoadingSuggestion ? (
                            <Loader2 className="absolute right-3 h-4 w-4 animate-spin text-muted-foreground" />
                          ) : value && value.trim() !== "" ? (
                            <CheckIcon className="absolute right-3 h-4 w-4 text-green-600" />
                          ) : null}
                        </div>
                      </div>
                    );
                  })}
                </div>

                // <div></div>
              )}

            <div className="mt-4 flex items-center space-x-2">
              <Button
                onClick={handleSaveMappings}
                disabled={
                  !salesforceCredentialsQuery.data
                    ?.doesOrganizationHaveSalesforceCredential ||
                  saveUtmMappingsMutation.isPending ||
                  utmFieldSuggestionsQuery.isLoading || // Disable while suggestions load
                  existingUtmMappingsQuery.isLoading // Disable while existing mappings load
                }
              >
                {saveUtmMappingsMutation.isPending ? "Saving..." : "Save"}
              </Button>

              {/* Success Feedback */}
              {mutationStatus === "success" && (
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircle2 className="mr-1 h-4 w-4" />
                  Mappings Saved Successfully
                </div>
              )}

              {/* Error Feedback */}
              {mutationStatus === "error" && (
                <div className="flex items-center text-sm text-red-600">
                  <XCircle className="mr-1 h-4 w-4" />
                  Error please try again.
                </div>
              )}
            </div>
          </div>
          <div className="mt-4 space-y-4 rounded-md border bg-muted p-4">
            <h3 className="text-sm font-semibold">
              Salesforce First Meeting Field Mapping
            </h3>
            <span className="text-sm text-muted-foreground">
              Configure how to identify first meetings in your Salesforce data.
            </span>

            {existingFirstMeetingMappingQuery.isLoading && (
              <p className="pt-2 text-sm text-muted-foreground">
                Loading settings...
              </p>
            )}
            {existingFirstMeetingMappingQuery.isError && (
              <p className="pt-2 text-sm text-red-500">
                Error loading settings:{" "}
                {existingFirstMeetingMappingQuery.error.message}
              </p>
            )}

            {!existingFirstMeetingMappingQuery.isLoading &&
              !existingFirstMeetingMappingQuery.isError && (
                <div className="space-y-4 pt-2">
                  {/* Entity Selection */}
                  <div className="space-y-2">
                    <label
                      htmlFor="entity-select"
                      className="text-sm font-medium"
                    >
                      In which Salesforce entity do you store first meetings?
                    </label>
                    <Select
                      value={firstMeetingMapping.entity}
                      onValueChange={(value) =>
                        handleFirstMeetingInputChange("entity", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select entity..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="contact">Contact</SelectItem>
                        <SelectItem value="lead">Lead</SelectItem>
                        <SelectItem value="opportunity">Opportunity</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Field Name Input */}
                  <div className="space-y-2">
                    <label
                      htmlFor="field-input"
                      className="text-sm font-medium"
                    >
                      What is the field name?
                    </label>
                    <Input
                      id="field-input"
                      placeholder="e.g., Date_BQ__c"
                      value={firstMeetingMapping.field}
                      onChange={(e) =>
                        handleFirstMeetingInputChange("field", e.target.value)
                      }
                    />
                  </div>

                  {/* Type Selection */}
                  <div className="space-y-2">
                    <label
                      htmlFor="type-select"
                      className="text-sm font-medium"
                    >
                      What type of field is this?
                    </label>
                    <Select
                      value={firstMeetingMapping.type}
                      onValueChange={(value) =>
                        handleFirstMeetingInputChange("type", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue
                          className="mt-2"
                          placeholder="Select field type..."
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="text">Text</SelectItem>
                        <SelectItem value="date">Date</SelectItem>
                        <SelectItem value="boolean">Checkbox</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="pl-1">
                      {/* <label className="text-sm font-medium">
                      How we determine first meeting
                    </label> */}
                      {/* <Input value="exists" disabled className="bg-muted" /> */}
                      {firstMeetingMapping.type === "boolean" ? (
                        <p className="text-xs text-muted-foreground">
                          We'll consider this a first meeting if the checkbox is
                          checked.
                        </p>
                      ) : firstMeetingMapping.type === "date" ? (
                        <p className="text-xs text-muted-foreground">
                          We'll consider this a first meeting if the field is
                          populated with a date
                        </p>
                      ) : (
                        <p className="text-xs text-muted-foreground">
                          We'll consider this a first meeting if the field is
                          populated with any value
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}

            <div className="mt-4 flex items-center space-x-2">
              <Button
                onClick={handleSaveFirstMeetingMapping}
                disabled={
                  !salesforceCredentialsQuery.data
                    ?.doesOrganizationHaveSalesforceCredential ||
                  saveFirstMeetingMappingMutation.isPending ||
                  !firstMeetingMapping.entity ||
                  !firstMeetingMapping.field ||
                  !firstMeetingMapping.type ||
                  existingFirstMeetingMappingQuery.isLoading
                }
              >
                {saveFirstMeetingMappingMutation.isPending
                  ? "Saving..."
                  : "Save"}
              </Button>

              {/* Success Feedback */}
              {firstMeetingMutationStatus === "success" && (
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircle2 className="mr-1 h-4 w-4" />
                  First Meeting Mapping Saved Successfully
                </div>
              )}

              {/* Error Feedback */}
              {firstMeetingMutationStatus === "error" && (
                <div className="flex items-center text-sm text-red-600">
                  <XCircle className="mr-1 h-4 w-4" />
                  Error please try again.
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
